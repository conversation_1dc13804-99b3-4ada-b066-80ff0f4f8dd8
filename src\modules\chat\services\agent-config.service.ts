import { Injectable, Logger } from '@nestjs/common';
import { AgentConfigQueries } from '../database';
import { 
  SystemAgentConfig, 
  SystemAgentConfigMap 
} from '../interfaces/system-agent-config.interface';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';

/**
 * Service for managing agent configuration data aggregation
 * 
 * This service orchestrates the complex process of building SystemAgentConfig
 * payloads from multiple database tables for multi-agent processing.
 */
@Injectable()
export class AgentConfigService {
  private readonly logger = new Logger(AgentConfigService.name);

  constructor(
    private readonly agentConfigQueries: AgentConfigQueries,
  ) {}

  /**
   * Build complete agent configuration map for multi-agent processing
   * @returns Promise<SystemAgentConfigMap> Complete agent configuration map
   */
  async buildAgentConfigMap(): Promise<SystemAgentConfigMap> {
    try {
      this.logger.log('Building agent configuration map...');
      
      const configMap = await this.agentConfigQueries.getAllSystemAgentConfigs();
      
      this.logger.log(`Built configuration for ${Object.keys(configMap).length} agents`);
      
      // Log agent details for debugging
      for (const [agentId, config] of Object.entries(configMap)) {
        this.logger.debug(`Agent ${agentId}: ${config.name} (${config.model.provider}/${config.model.name})`);
      }
      
      return configMap;
    } catch (error) {
      this.logger.error('Failed to build agent configuration map', error);
      throw new AppException(CHAT_ERROR_CODES.AGENT_CONFIG_BUILD_FAILED);
    }
  }

  /**
   * Get agent configuration by ID
   * @param agentId Agent ID
   * @returns Promise<SystemAgentConfig | null>
   */
  async getAgentConfig(agentId: string): Promise<SystemAgentConfig | null> {
    try {
      this.logger.log(`Getting agent configuration for ID: ${agentId}`);
      
      const config = await this.agentConfigQueries.getSystemAgentConfigById(agentId);
      
      if (config) {
        this.logger.debug(`Found config for agent ${agentId}: ${config.name}`);
      } else {
        this.logger.warn(`No configuration found for agent ID: ${agentId}`);
      }
      
      return config;
    } catch (error) {
      this.logger.error(`Failed to get agent configuration for ID: ${agentId}`, error);
      throw new AppException(CHAT_ERROR_CODES.AGENT_CONFIG_BUILD_FAILED);
    }
  }

  /**
   * Validate agent configuration completeness
   * @param config Agent configuration to validate
   * @returns boolean True if configuration is valid
   */
  validateAgentConfig(config: SystemAgentConfig): boolean {
    try {
      // Check required fields
      if (!config.id || !config.name || !config.model) {
        this.logger.warn(`Invalid agent config: missing required fields for ${config.id}`);
        return false;
      }

      // Check model configuration
      if (!config.model.name || !config.model.provider) {
        this.logger.warn(`Invalid model config for agent ${config.id}: missing model name or provider`);
        return false;
      }

      // Check API keys
      if (!config.model.apiKeys || config.model.apiKeys.length === 0) {
        this.logger.warn(`No API keys found for agent ${config.id}`);
        return false;
      }

      // Check for empty API keys
      const validApiKeys = config.model.apiKeys.filter(key => key && key.trim().length > 0);
      if (validApiKeys.length === 0) {
        this.logger.warn(`No valid API keys found for agent ${config.id}`);
        return false;
      }

      this.logger.debug(`Agent config validation passed for ${config.id}`);
      return true;
    } catch (error) {
      this.logger.error(`Error validating agent config for ${config.id}`, error);
      return false;
    }
  }

  /**
   * Get supervisor agent configuration
   * @returns Promise<SystemAgentConfig | null>
   */
  async getSupervisorAgent(): Promise<SystemAgentConfig | null> {
    try {
      this.logger.log('Getting supervisor agent configuration...');
      
      const configMap = await this.buildAgentConfigMap();
      
      // Find supervisor agent (is_supervisor = true)
      for (const config of Object.values(configMap)) {
        // Note: We'll need to add supervisor flag to the query result
        // For now, we can assume the first agent is supervisor or use naming convention
        if (config.name.toLowerCase().includes('supervisor') || 
            config.name.toLowerCase().includes('manager')) {
          this.logger.log(`Found supervisor agent: ${config.name} (${config.id})`);
          return config;
        }
      }
      
      // If no explicit supervisor found, return the first agent
      const firstAgent = Object.values(configMap)[0];
      if (firstAgent) {
        this.logger.log(`Using first agent as supervisor: ${firstAgent.name} (${firstAgent.id})`);
        return firstAgent;
      }
      
      this.logger.warn('No supervisor agent found');
      return null;
    } catch (error) {
      this.logger.error('Failed to get supervisor agent', error);
      throw new AppException(CHAT_ERROR_CODES.AGENT_CONFIG_BUILD_FAILED);
    }
  }

  /**
   * Get worker agents (non-supervisor agents)
   * @returns Promise<SystemAgentConfig[]>
   */
  async getWorkerAgents(): Promise<SystemAgentConfig[]> {
    try {
      this.logger.log('Getting worker agent configurations...');
      
      const configMap = await this.buildAgentConfigMap();
      const supervisor = await this.getSupervisorAgent();
      
      // Filter out supervisor agent
      const workers = Object.values(configMap).filter(config => 
        config.id !== supervisor?.id
      );
      
      this.logger.log(`Found ${workers.length} worker agents`);
      
      return workers;
    } catch (error) {
      this.logger.error('Failed to get worker agents', error);
      throw new AppException(CHAT_ERROR_CODES.AGENT_CONFIG_BUILD_FAILED);
    }
  }

  /**
   * Get configuration summary for logging/debugging
   * @returns Promise<object> Configuration summary
   */
  async getConfigSummary(): Promise<object> {
    try {
      const configMap = await this.buildAgentConfigMap();
      
      const summary = {
        totalAgents: Object.keys(configMap).length,
        agents: Object.values(configMap).map(config => ({
          ...config
        }))
      };
      
      return summary;
    } catch (error) {
      this.logger.error('Failed to get configuration summary', error);
      throw new AppException(CHAT_ERROR_CODES.AGENT_CONFIG_BUILD_FAILED);
    }
  }
}
