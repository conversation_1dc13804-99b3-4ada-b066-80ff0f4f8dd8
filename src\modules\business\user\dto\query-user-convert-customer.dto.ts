import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp khách hàng chuyển đổi
 */
export enum UserConvertCustomerSortField {
  ID = 'id',
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  PLATFORM = 'platform',
}

/**
 * DTO cho các tham số truy vấn danh sách khách hàng chuyển đổi
 */
export class QueryUserConvertCustomerDto extends QueryDto {

  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    required: false,
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  @IsOptional()
  @IsString()
  agentId?: string;

  /**
   * <PERSON><PERSON> đè thuộc tính sortBy từ QueryDto để sử dụng enum cụ thể
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserConvertCustomerSortField,
    default: UserConvertCustomerSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserConvertCustomerSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(UserConvertCustomerSortField).join(', ')}`,
  })
  override sortBy?: UserConvertCustomerSortField = UserConvertCustomerSortField.CREATED_AT;

  /**
   * Ghi đè thuộc tính sortDirection từ QueryDto để thay đổi giá trị mặc định
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, {
    message: `Hướng sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,
  })
  override sortDirection?: SortDirection = SortDirection.DESC;
}
