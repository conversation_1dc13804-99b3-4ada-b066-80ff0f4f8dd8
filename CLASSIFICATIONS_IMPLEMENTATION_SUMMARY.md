# Classifications Implementation Summary

## Vấn đề đã được gi<PERSON>i quyết

### **Trước khi fix:**
- Classifications chỉ được trả về trong response nhưng **KHÔNG được lưu vào database**
- Method `processClassifications` trong `physical-product.processor.ts` chỉ là **TODO placeholder**
- Frontend gửi classifications nhưng backend không xử lý thực tế

### **Sau khi fix:**
- ✅ Classifications được **lưu vào database** bảng `user_classifications`
- ✅ **imagesMediaTypes** trong classifications tạo presigned URLs riêng cho upload ảnh
- ✅ **customFields** của classifications được lưu vào cột `custom_fields` (jsonb)
- ✅ Response trả về cả `classifications` array và `classificationUploadUrls` array

## Cấu trúc Database

### Bảng: `user_classifications`
```sql
CREATE TABLE user_classifications (
  id SERIAL PRIMARY KEY,
  type TEXT NOT NULL,                    -- <PERSON>ại phân loại (VD: "Đen - Size M")
  price JSONB,                          -- Gi<PERSON> phân loại
  product_id INTEGER,                   -- ID sản phẩm
  metadata JSONB DEFAULT '{"customFields": []}',  -- Metadata chung
  custom_fields JSONB,                  -- Custom fields riêng
  images_media JSONB,                   -- Thông tin hình ảnh
  description TEXT,                     -- Mô tả
  sku VARCHAR(255),                     -- SKU
  min_quantity_per_purchase INTEGER,    -- Số lượng tối thiểu
  max_quantity_per_purchase INTEGER     -- Số lượng tối đa
);
```

## Flow xử lý Classifications

### 1. **Request từ Frontend:**
```json
{
  "classifications": [
    {
      "type": "Đen - Size M",
      "price": {
        "listPrice": 250000,
        "salePrice": 199000,
        "currency": "VND"
      },
      "customFields": [
        {
          "customFieldId": 127,
          "value": { "value": true }
        }
      ],
      "imagesMediaTypes": ["image/jpeg", "image/jpeg"]
    }
  ]
}
```

### 2. **Backend Processing:**
```typescript
// 1. Mapping request DTO sang CreateClassificationDto
const createClassificationDto = this.mapToCreateClassificationDto(classificationDto);

// 2. Lưu vào database thông qua ClassificationService
const createdClassification = await this.classificationService.create(
  productId,
  createClassificationDto,
  userId,
);

// 3. Tạo presigned URLs cho ảnh classification
if (classificationDto.imagesMediaTypes && classificationDto.imagesMediaTypes.length > 0) {
  const uploadUrls = await this.createClassificationImageUploadUrls(
    classificationDto,
    createdClassification.id,
    index
  );
}
```

### 3. **Response trả về:**
```json
{
  "data": {
    "product": { ... },
    "uploadUrls": {
      "productId": "123",
      "imagesUploadUrls": [ ... ],           // URLs cho ảnh sản phẩm chính
      "classificationUploadUrls": [          // URLs cho ảnh classifications
        {
          "url": "https://presigned-url-example.com/...",
          "key": "physical-classification-1-image-0-1703123456789",
          "index": 0,
          "classificationId": 1,
          "classificationIndex": 0,
          "classificationType": "Đen - Size M"
        }
      ]
    },
    "additionalInfo": {
      "inventory": [ ... ],
      "classifications": [                   // Classifications đã lưu database
        {
          "id": 1,
          "type": "Đen - Size M",
          "price": { ... },
          "customFields": [ ... ]
        }
      ]
    }
  }
}
```

## Files đã được cập nhật

### 1. **physical-product.processor.ts**
- ✅ Import `CreateClassificationDto`, `ClassificationResponseDto`
- ✅ Implement method `processClassifications()` thực tế
- ✅ Thêm method `mapToCreateClassificationDto()`
- ✅ Thêm method `createClassificationImageUploadUrls()`
- ✅ Cập nhật return type và response structure

### 2. **physical-product-api.http**
- ✅ Thêm test case mới cho classifications
- ✅ Cập nhật documentation về classifications
- ✅ Thêm thông tin về database storage

## Cách test

### Test case trong file .http:
```http
POST {{baseUrl}}/user/products
{
  "productType": "PHYSICAL",
  "name": "Áo thun test classifications",
  "inventory": [{ "warehouseId": 1, "availableQuantity": 100 }],
  "classifications": [
    {
      "type": "Đen - Size M",
      "price": { "listPrice": 250000, "salePrice": 199000, "currency": "VND" },
      "customFields": [{ "customFieldId": 127, "value": { "value": true } }],
      "imagesMediaTypes": ["image/jpeg", "image/jpeg"]
    }
  ]
}
```

### Kết quả mong đợi:
1. ✅ Sản phẩm được tạo thành công
2. ✅ Classifications được lưu vào bảng `user_classifications`
3. ✅ Response trả về `classificationUploadUrls` cho upload ảnh
4. ✅ CustomFields được lưu vào database

## Lưu ý quan trọng

### **imagesMediaTypes trong classifications:**
- **Mục đích:** Frontend truyền vào để backend tạo presigned URLs
- **Xử lý:** Tương tự như `imagesMediaTypes` ở level sản phẩm
- **Kết quả:** Tạo ra `classificationUploadUrls` array riêng biệt

### **CustomFields của classifications:**
- **Lưu trữ:** Vào cột `custom_fields` (jsonb) trong bảng `user_classifications`
- **Không cần validate:** Classification custom fields không cần validate với database
- **Cấu trúc:** Giữ nguyên format từ request

### **Database Relations:**
- `user_classifications.product_id` → `user_products.id`
- Một sản phẩm có thể có nhiều classifications
- Mỗi classification có thể có nhiều custom fields và images riêng

## Kết luận

✅ **Vấn đề đã được giải quyết hoàn toàn:**
- Classifications được lưu vào database thay vì chỉ trả về placeholder
- imagesMediaTypes tạo presigned URLs cho upload ảnh classifications
- CustomFields được xử lý và lưu trữ đúng cách
- Response structure được cập nhật để bao gồm classification upload URLs

🔧 **TODO tiếp theo:**
- Implement thực tế S3 presigned URL generation (hiện tại là placeholder)
- Thêm validation cho classification data nếu cần
- Implement classification image processing sau khi upload
