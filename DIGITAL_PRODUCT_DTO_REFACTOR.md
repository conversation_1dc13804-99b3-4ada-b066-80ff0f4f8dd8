# Refactor DigitalProductCreateDto - Chuyển từ AdvancedInfo sang Direct Fields

## Tóm tắt thay đổi
Đã refactor DigitalProductCreateDto bằng cách chuyển các trường từ DigitalAdvancedInfoDto vào trực tiếp và thay đổi `variantMetadata: VariantMetadataDto` thành `classifications: DigitalClassificationDto[]`.

## Các file đã được tạo/cập nhật

### 1. Tạo DigitalClassificationDto
**File:** `src/modules/business/user/dto/digital-classification.dto.ts`

#### DTOs mới:
- **`DigitalClassificationDto`**: DTO cho tạo digital classification (chuyển từ VariantDto)
- **`DigitalClassificationResponseDto`**: DTO cho response
- **`UpdateDigitalClassificationDto`**: DTO cho update

#### Các trường chính:
```typescript
export class DigitalClassificationDto {
  name: string;                    // Tên phân loại
  sku: string;                     // Mã SKU
  availableQuantity: number;       // Số lượng có sẵn
  minQuantityPerPurchase: number;  // Số lượng tối thiểu mỗi lần mua
  maxQuantityPerPurchase: number;  // Số lượng tối đa mỗi lần mua
  price: HasPriceDto;             // Giá phân loại
  customFields?: CustomFieldInputDto[]; // Custom fields
  imagesMediaTypes: string[];      // Loại hình ảnh
  description: string;             // Mô tả
}
```

### 2. Refactor DigitalProductCreateDto
**File:** `src/modules/business/user/dto/request/create/digital-product-create.dto.ts`

#### Thay đổi cấu trúc:
**Trước:**
```typescript
export class DigitalProductCreateDto extends BaseProductDto {
  advancedInfo: DigitalAdvancedInfoDto; // Chứa tất cả thông tin
  price: HasPriceDto;
}
```

**Sau:**
```typescript
export class DigitalProductCreateDto extends BaseProductDto {
  // Các trường từ DigitalAdvancedInfoDto được chuyển vào trực tiếp
  purchaseCount: number;
  digitalFulfillmentFlow: DigitalFulfillmentFlowDto;
  digitalOutput: DigitalOutputDto;
  classifications: DigitalClassificationDto[]; // Thay thế variantMetadata
  price: HasPriceDto;
}
```

#### Các DTO helper được thêm:
```typescript
// Types
export type DeliveryMethodType = 'DASHBOARD' | 'EMAIL' | 'SMS' | 'DIRECT_MESSAGE' | 'ZALO' | 'AUTO_ACTIVE';
export type DeliveryTimingType = 'IMMEDIATE' | 'DELAYED';
export type OutputType = 'DOWNLOAD_LINK' | 'ACCESS_CODE' | 'ACCOUNT_INFO' | 'CONTENT';

// DTOs
export class DigitalFulfillmentFlowDto {
  deliveryMethod: DeliveryMethodType;
  deliveryTiming: DeliveryTimingType;
}

export class DigitalOutputDto {
  outputType: OutputType;
  accessLink: string;
  usageInstructions: string;
}
```

### 3. Cập nhật Digital Product Processor
**File:** `src/modules/business/user/services/processors/create/digital-product.processor.ts`

#### Thay đổi chính:
```typescript
// Trước
const variantMetadata = await this.processDigitalVariantMetadata(dto);
const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto.advancedInfo, []);

// Sau
const classificationsMetadata = await this.processDigitalClassificationsMetadata(dto);
const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto, []);
```

#### Methods được cập nhật:
- **`validateDigitalProductData()`**: Validate classifications thay vì advancedInfo
- **`processDigitalClassificationsMetadata()`**: Xử lý classifications metadata
- **`createAdvancedInfo()`**: Sử dụng DTO trực tiếp thay vì advancedInfo
- **`createAdvancedImagesUploadUrls()`**: Xử lý hình ảnh classifications

### 4. Cập nhật Digital Product Handler
**File:** `src/modules/business/user/services/product-creation/handlers/digital-product.handler.ts`

#### Validation được cập nhật:
```typescript
// Trước
if (!dto.advancedInfo) {
  throw new Error('Advanced info is required for digital products');
}

// Sau
if (!dto.classifications || dto.classifications.length === 0) {
  throw new Error('Classifications are required for digital products');
}
```

#### Methods được cập nhật:
- **`validate()`**: Validate classifications và digital fields
- **`createBaseProduct()`**: Sử dụng processClassificationsMetadata
- **`processAdvancedInfo()`**: Sử dụng fields trực tiếp từ DTO
- **`processAdvancedImages()`**: Xử lý classification images
- **`processClassificationsMetadata()`**: Method mới thay thế processVariantMetadata

### 5. Cập nhật Index Export
**File:** `src/modules/business/user/dto/index.ts`
```typescript
// Export digital classification DTO
export * from './digital-classification.dto';
```

## Mapping từ cũ sang mới

### DigitalAdvancedInfoDto → DigitalProductCreateDto
| Cũ (DigitalAdvancedInfoDto) | Mới (DigitalProductCreateDto) |
|----------------------------|-------------------------------|
| `purchaseCount` | `purchaseCount` |
| `digitalFulfillmentFlow` | `digitalFulfillmentFlow` |
| `digitalOutput` | `digitalOutput` |
| `variantMetadata: VariantMetadataDto` | `classifications: DigitalClassificationDto[]` |

### VariantDto → DigitalClassificationDto
| Cũ (VariantDto) | Mới (DigitalClassificationDto) |
|-----------------|--------------------------------|
| `name` | `name` |
| `sku` | `sku` |
| `availableQuantity` | `availableQuantity` |
| `minQuantityPerPurchase` | `minQuantityPerPurchase` |
| `maxQuantityPerPurchase` | `maxQuantityPerPurchase` |
| `price` | `price` |
| `customFields` | `customFields` |
| `imagesMediaTypes` | `imagesMediaTypes` |
| `description` | `description` |

## Lợi ích của refactor

### 1. Cấu trúc rõ ràng hơn
- Loại bỏ nested structure phức tạp
- Các trường được expose trực tiếp ở top level
- Dễ hiểu và sử dụng hơn

### 2. Tên gọi chính xác hơn
- `classifications` thay vì `variants` phù hợp với business logic
- `DigitalClassificationDto` rõ ràng hơn `VariantDto`

### 3. Validation đơn giản hơn
- Validate trực tiếp các trường thay vì nested validation
- Error messages rõ ràng hơn

### 4. Maintainability
- Ít dependency giữa các DTO
- Dễ extend và modify

## Ví dụ sử dụng

### Request tạo digital product:
```json
{
  "name": "Khóa học React Advanced",
  "productType": "DIGITAL",
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "ACCESS_CODE",
    "accessLink": "https://course.example.com/activate",
    "usageInstructions": "Sử dụng mã code để kích hoạt khóa học"
  },
  "classifications": [
    {
      "name": "Basic",
      "sku": "REACT-BASIC-001",
      "availableQuantity": 100,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 500000,
        "salePrice": 400000,
        "currency": "VND"
      },
      "description": "Phiên bản cơ bản",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": []
    },
    {
      "name": "Premium",
      "sku": "REACT-PREMIUM-001",
      "availableQuantity": 50,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 1000000,
        "salePrice": 800000,
        "currency": "VND"
      },
      "description": "Phiên bản cao cấp với mentor support",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": []
    }
  ],
  "price": {
    "listPrice": 500000,
    "salePrice": 400000,
    "currency": "VND"
  }
}
```

## Tương thích ngược
- Các API endpoints giữ nguyên
- Response format có thể được maintain thông qua mapper
- Database schema không thay đổi

## Breaking Changes
- `DigitalAdvancedInfoDto` không còn được sử dụng trong `DigitalProductCreateDto`
- `VariantMetadataDto` và `VariantDto` được thay thế bằng `DigitalClassificationDto[]`
- Validation logic thay đổi từ nested sang flat structure

## Migration Guide
1. Update frontend code để sử dụng structure mới
2. Update any existing tests
3. Update documentation
4. Verify all digital product creation flows work correctly
