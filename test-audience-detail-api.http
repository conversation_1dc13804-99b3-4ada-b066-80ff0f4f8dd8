### Test API lấy chi tiết audience với configJson

# L<PERSON>y chi tiết audience theo ID
GET http://localhost:3000/marketing/audiences/1
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

###

# Response mong đợi:
# {
#   "success": true,
#   "message": "Thông tin audience",
#   "data": {
#     "id": 1,
#     "name": "Nguyễn Văn A",
#     "email": "<EMAIL>",
#     "phone": "+84912345678",
#     "countryCode": "+84",
#     "avatar": null,
#     "customFields": [
#       {
#         "id": 1,
#         "audienceId": 1,
#         "fieldId": 5,
#         "fieldValue": true,
#         "configJson": {
#           "id": "nam_nu",
#           "type": "select",
#           "label": "Nam hay Nữ",
#           "options": [
#             {"label": "Nam", "value": "nam"},
#             {"label": "Nữ", "value": "nu"}
#           ],
#           "validation": {
#             "options": [
#               {"label": "Nam", "value": "nam"},
#               {"label": "Nữ", "value": "nu"}
#             ]
#           },
#           "displayName": "Nam hay Nữ"
#         },
#         "createdAt": 1619171200,
#         "updatedAt": 1619171200
#       }
#     ],
#     "tags": [],
#     "createdAt": 1619171200,
#     "updatedAt": 1619171200
#   }
# }

### Test với audience khác
GET http://localhost:3000/marketing/audiences/2
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

### Test danh sách audience (cũng sẽ có configJson)
GET http://localhost:3000/marketing/audiences?page=1&limit=10
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json
