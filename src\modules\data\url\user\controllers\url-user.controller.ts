import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Logger, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { SortDirection } from '@common/dto/query.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { UrlListResponseSchema, UrlSchema } from '../../schemas/url.schema';
import { CreateUrlDto } from '../../schemas/create-url.dto';
import { UpdateUrlDto } from '../../schemas/update-url.dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { UrlUserService } from '../services/url-user.service';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { FindAllUrlDto } from '../dto/find-all-url.dto';
import { CrawlDto } from '../dto/crawl.dto';
import { DeleteUrlsUserDto } from '../dto/delete-urls-user.dto';
import {
  StartCrawlWithTrackingDto,
  CrawlProgressResponseDto,
  CrawlSessionListResponseDto,
  StartCrawlResponseDto
} from '../dto/crawl-progress.dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { URL_ERROR_CODES } from '../../exceptions/url-user.exception';
import { ErrorCode } from '@common/exceptions';

/**
 * Controller xử lý các endpoint liên quan đến URL cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_URL)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  UrlSchema,
  UrlListResponseSchema,
  CreateUrlDto,
  UpdateUrlDto,
  DeleteUrlsUserDto,
  FindAllUrlDto,
  StartCrawlWithTrackingDto,
  CrawlProgressResponseDto,
  CrawlSessionListResponseDto,
  StartCrawlResponseDto
)
@Controller('data/url')
@UseGuards(JwtUserGuard)
export class UrlUserController {
  private readonly logger = new Logger(UrlUserController.name);

  constructor(private readonly urlUserService: UrlUserService) {}

  /**
   * Lấy danh sách URL của người dùng với phân trang và tìm kiếm
   */
  @ApiOperation({
    summary: 'Lấy danh sách URL của người dùng',
    description: 'Lấy danh sách URL thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọc theo tags, type và phân trang'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (bắt đầu từ 1)',
    type: Number,
    example: 1,
    minimum: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng kết quả mỗi trang',
    type: Number,
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Trường sắp xếp',
    type: String,
    example: 'createdAt'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    description: 'Hướng sắp xếp',
    enum: ['ASC', 'DESC'],
    example: 'DESC'
  })
  @ApiQuery({
    name: 'keyword',
    required: false,
    description: 'Từ khóa tìm kiếm trong title, content và URL',
    type: String,
    example: 'nestjs tutorial'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Loại URL cần lọc',
    type: String,
    example: 'web'
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Các thẻ cần lọc (có thể dùng dấu phẩy để phân tách nhiều thẻ)',
    type: String,
    example: 'nestjs,tutorial,programming'
  })
  @ApiQuery({
    name: 'ownedByEnum',
    required: false,
    description: 'Loại người sở hữu URL',
    enum: ['ADMIN', 'USER'],
    example: 'USER'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách URL thành công',
    schema: ApiResponseDto.getPaginatedSchema(UrlSchema),
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    URL_ERROR_CODES.URL_FETCH_FAILED,
    URL_ERROR_CODES.URL_INVALID_SEARCH_PARAMS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get()
  @HttpCode(HttpStatus.OK)
  async findAll(@CurrentUser() user: any, @Query() queryParams: FindAllUrlDto) {
    const userId = user.id;
    this.logger.log(
      `Finding all URLs for user: ${userId}, page: ${queryParams.page}, limit: ${queryParams.limit}`,
    );

    // Convert string parameters to appropriate types if needed
    const page = queryParams.page || 1;
    const limit = queryParams.limit || 10;
    const sortBy = queryParams.sortBy || 'createdAt';
    const sortDirection = queryParams.sortDirection || SortDirection.DESC;

    // Đảm bảo tags luôn là mảng
    const tags = queryParams.tags || [];

    const findAllUrlDto = {
      page,
      limit,
      search: queryParams.keyword,
      sortBy,
      sortDirection,
    };

    const result = await this.urlUserService.findUrlsByOwner(
      userId,
      findAllUrlDto,
    );

    this.logger.log(`Found ${result.items.length} URLs for user: ${userId}`);

    return ApiResponseDto.paginated(result, 'Lấy danh sách URL thành công');
  }

  /**
   * Lấy thông tin chi tiết URL theo ID
   */
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết URL theo ID',
    description: 'Lấy thông tin chi tiết của một URL cụ thể theo ID. Chỉ có thể lấy URL thuộc sở hữu của người dùng hiện tại.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của URL (UUID)',
    type: String,
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết URL thành công',
    schema: ApiResponseDto.getSchema(UrlSchema),
  })
  @ApiErrorResponse(
    URL_ERROR_CODES.URL_NOT_FOUND,
    URL_ERROR_CODES.URL_ACCESS_DENIED,
    URL_ERROR_CODES.URL_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async findOne(@CurrentUser() user: any, @Param('id') id: string) {
    const userId = user.id;
    this.logger.log(`Finding URL with ID: ${id} for user: ${userId}`);

    const result = await this.urlUserService.findUrlById(userId, id);

    this.logger.log(`URL with ID: ${id} found for user: ${userId}`);

    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết URL thành công');
  }

  /**
   * Tạo URL mới
   */
  @ApiOperation({
    summary: 'Tạo URL mới',
    description: 'Tạo một URL mới trong hệ thống. URL sẽ được gán cho người dùng hiện tại.'
  })
  @ApiBody({
    type: CreateUrlDto,
    description: 'Thông tin URL cần tạo',
    examples: {
      'basic': {
        summary: 'Tạo URL cơ bản',
        description: 'Tạo URL với thông tin cơ bản',
        value: {
          url: 'https://example.com/article',
          title: 'Bài viết hay về NestJS',
          description: 'Một bài viết chi tiết về framework NestJS',
          tags: ['nestjs', 'nodejs', 'backend']
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'URL đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UrlSchema),
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    URL_ERROR_CODES.URL_INVALID_FORMAT,
    URL_ERROR_CODES.URL_ALREADY_EXISTS,
    URL_ERROR_CODES.URL_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@CurrentUser() user: any, @Body() createUrlDto: CreateUrlDto) {
    const userId = user.id;
    this.logger.log(`Creating new URL for user: ${userId}`);
    this.logger.debug(`URL data: ${JSON.stringify(createUrlDto)}`);

    const result = await this.urlUserService.createUrl(userId, createUrlDto);

    this.logger.log(`URL created successfully with ID: ${result.id}`);

    return ApiResponseDto.created(result, 'URL đã được tạo thành công');
  }

  /**
   * Cập nhật thông tin URL
   */
  @ApiOperation({
    summary: 'Cập nhật thông tin URL',
    description: 'Cập nhật thông tin của một URL cụ thể. Chỉ có thể cập nhật URL thuộc sở hữu của người dùng hiện tại.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của URL (UUID)',
    type: String,
    example: '550e8400-e29b-41d4-a716-************'
  })
  @ApiBody({
    type: UpdateUrlDto,
    description: 'Thông tin cần cập nhật',
    examples: {
      'updateTitle': {
        summary: 'Cập nhật tiêu đề',
        description: 'Cập nhật tiêu đề của URL',
        value: {
          title: 'Tiêu đề mới cho bài viết'
        }
      },
      'updateAll': {
        summary: 'Cập nhật toàn bộ thông tin',
        description: 'Cập nhật tất cả thông tin của URL',
        value: {
          title: 'Tiêu đề mới',
          description: 'Mô tả mới cho URL',
          tags: ['updated', 'new-tags']
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'URL đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UrlSchema),
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    URL_ERROR_CODES.URL_NOT_FOUND,
    URL_ERROR_CODES.URL_ACCESS_DENIED,
    URL_ERROR_CODES.URL_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  async update(
    @CurrentUser() user: any,
    @Param('id') id: string,
    @Body() updateUrlDto: UpdateUrlDto,
  ) {
    const userId = user.id;
    this.logger.log(`Updating URL with ID: ${id} for user: ${userId}`);
    this.logger.debug(`Update data: ${JSON.stringify(updateUrlDto)}`);

    const result = await this.urlUserService.updateUrl(
      id,
      userId,
      updateUrlDto,
    );

    this.logger.log(`URL with ID: ${id} updated successfully`);

    return ApiResponseDto.success(result, 'URL đã được cập nhật thành công');
  }

  /**
   * Xóa nhiều URL
   */
  @ApiOperation({
    summary: 'Xóa nhiều URL',
    description: 'Xóa nhiều URL cùng lúc theo danh sách ID. Chỉ có thể xóa URL thuộc sở hữu của người dùng hiện tại.'
  })
  @ApiBody({
    type: DeleteUrlsUserDto,
    description: 'Danh sách ID của các URL cần xóa',
    examples: {
      'single': {
        summary: 'Xóa một URL',
        description: 'Xóa một URL duy nhất',
        value: {
          ids: ['550e8400-e29b-41d4-a716-************']
        }
      },
      'multiple': {
        summary: 'Xóa nhiều URL',
        description: 'Xóa nhiều URL cùng lúc',
        value: {
          ids: [
            '550e8400-e29b-41d4-a716-************',
            '550e8400-e29b-41d4-a716-446655440001',
            '550e8400-e29b-41d4-a716-446655440002'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'URL đã được xóa thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Đã xóa thành công 3 URL' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    URL_ERROR_CODES.URL_NOT_FOUND,
    URL_ERROR_CODES.URL_ACCESS_DENIED,
    URL_ERROR_CODES.URL_DELETE_FAILED,
    URL_ERROR_CODES.URL_INVALID_PARAMS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Delete('batch')
  @HttpCode(HttpStatus.OK)
  async remove(
    @CurrentUser() user: any,
    @Body() deleteUrlsDto: DeleteUrlsUserDto,
  ) {
    const userId = user.id;
    this.logger.log(
      `Deleting URLs with IDs: ${JSON.stringify(deleteUrlsDto.ids)} for user: ${userId}`,
    );

    await this.urlUserService.deleteUrls(deleteUrlsDto.ids, userId);

    this.logger.log(
      `Successfully deleted ${deleteUrlsDto.ids.length} URLs for user: ${userId}`,
    );

    return ApiResponseDto.success(
      null,
      `Đã xóa thành công ${deleteUrlsDto.ids.length} URL`,
    );
  }


  /**
   * Bắt đầu crawl với tracking session
   */
  @ApiOperation({
    summary: 'Bắt đầu crawl URL với theo dõi tiến độ',
    description: 'Tạo session crawl và theo dõi tiến độ thời gian thực. Session sẽ được tạo để theo dõi quá trình crawl và trích xuất nội dung từ URL.'
  })
  @ApiBody({
    type: StartCrawlWithTrackingDto,
    description: 'Thông tin URL cần crawl',
    examples: {
      'basic': {
        summary: 'Crawl URL cơ bản',
        description: 'Bắt đầu crawl một URL với cấu hình cơ bản',
        value: {
          url: 'https://example.com/article',
          title: 'Bài viết về NestJS',
          description: 'Crawl bài viết hướng dẫn NestJS',
          tags: ['nestjs', 'tutorial']
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đã bắt đầu crawl thành công',
    schema: ApiResponseDto.getSchema(StartCrawlResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    URL_ERROR_CODES.URL_INVALID_FORMAT,
    URL_ERROR_CODES.CRAWL_SESSION_CREATION_FAILED,
    URL_ERROR_CODES.URL_CRAWL_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Post('crawl/start')
  @HttpCode(HttpStatus.OK)
  async startCrawlWithTracking(
    @CurrentUser() user: any,
    @Body() crawlDto: StartCrawlWithTrackingDto
  ) {
    const userId = user.id;
    this.logger.log(
      `Bắt đầu crawl với tracking cho user: ${userId}, URL: ${crawlDto.url}`
    );

    const result = await this.urlUserService.startCrawlWithTracking(userId, crawlDto);

    this.logger.log(`Đã tạo crawl session: ${result.sessionId}`);

    return ApiResponseDto.success(result, result.message);
  }

  /**
   * Lấy tiến độ crawl theo session ID
   */
  @ApiOperation({
    summary: 'Lấy tiến độ crawl theo session ID',
    description: 'Theo dõi tiến độ crawl thời gian thực. Trả về thông tin chi tiết về trạng thái, tiến độ và kết quả của quá trình crawl.'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session crawl (UUID)',
    type: String,
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin tiến độ crawl thành công',
    schema: ApiResponseDto.getSchema(CrawlProgressResponseDto)
  })
  @ApiErrorResponse(
    URL_ERROR_CODES.CRAWL_SESSION_NOT_FOUND,
    URL_ERROR_CODES.CRAWL_SESSION_ACCESS_DENIED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get('crawl/progress/:sessionId')
  @HttpCode(HttpStatus.OK)
  async getCrawlProgress(
    @CurrentUser() user: any,
    @Param('sessionId') sessionId: string
  ) {
    const userId = user.id;
    this.logger.debug(`Lấy tiến độ crawl cho session: ${sessionId}, user: ${userId}`);

    const result = await this.urlUserService.getCrawlProgress(userId, sessionId);

    return ApiResponseDto.success(result, 'Lấy thông tin tiến độ crawl thành công');
  }

  /**
   * Lấy danh sách sessions crawl của user
   */
  @ApiOperation({
    summary: 'Lấy danh sách sessions crawl',
    description: 'Lấy danh sách tất cả sessions crawl của người dùng với khả năng lọc theo trạng thái và phân trang'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (bắt đầu từ 1)',
    type: Number,
    example: 1,
    minimum: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng items mỗi trang',
    type: Number,
    example: 20,
    minimum: 1,
    maximum: 100
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Lọc theo trạng thái crawl session',
    enum: ['running', 'completed', 'error', 'cancelled'],
    example: 'completed'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách sessions crawl thành công',
    schema: ApiResponseDto.getSchema(CrawlSessionListResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get('crawl/sessions')
  @HttpCode(HttpStatus.OK)
  async getUserCrawlSessions(
    @CurrentUser() user: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string
  ) {
    const userId = user.id;
    this.logger.debug(
      `Lấy danh sách sessions cho user: ${userId}, page: ${page}, limit: ${limit}, status: ${status}`
    );

    const result = await this.urlUserService.getUserCrawlSessions(
      userId,
      page || 1,
      limit || 20,
      status
    );

    this.logger.debug(`Tìm thấy ${result.total} sessions cho user: ${userId}`);

    return ApiResponseDto.success(result, 'Lấy danh sách sessions crawl thành công');
  }

  /**
   * Hủy session crawl đang chạy
   */
  @ApiOperation({
    summary: 'Hủy session crawl đang chạy',
    description: 'Hủy session crawl đang trong trạng thái running. Chỉ có thể hủy session thuộc sở hữu của người dùng hiện tại và đang ở trạng thái running.'
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session crawl (UUID)',
    type: String,
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đã hủy session thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Đã hủy session crawl thành công' },
        result: { type: 'boolean', example: true }
      }
    }
  })
  @ApiErrorResponse(
    URL_ERROR_CODES.CRAWL_SESSION_NOT_FOUND,
    URL_ERROR_CODES.CRAWL_SESSION_ACCESS_DENIED,
    URL_ERROR_CODES.CRAWL_SESSION_INVALID_STATUS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Delete('crawl/sessions/:sessionId')
  @HttpCode(HttpStatus.OK)
  async cancelCrawlSession(
    @CurrentUser() user: any,
    @Param('sessionId') sessionId: string
  ) {
    const userId = user.id;
    this.logger.log(`Hủy crawl session: ${sessionId} cho user: ${userId}`);

    const result = await this.urlUserService.cancelCrawlSession(userId, sessionId);

    if (result) {
      this.logger.log(`Đã hủy session thành công: ${sessionId}`);
      return ApiResponseDto.success(result, 'Đã hủy session crawl thành công');
    } else {
      this.logger.warn(`Không thể hủy session: ${sessionId}`);
      return ApiResponseDto.success(false, 'Không thể hủy session');
    }
  }
}
