import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin agent con trong multi-agent system
 */
export class MultiAgentResponseDto {
  /**
   * ID của agent con
   */
  @ApiProperty({
    description: 'ID của agent con',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  childAgentId: string;

  /**
   * Tên của agent con
   */
  @ApiProperty({
    description: 'Tên của agent con',
    example: 'Marketing Assistant',
  })
  name: string;

  /**
   * Avatar của agent con
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent con',
    example: 'https://cdn.example.com/avatars/marketing-assistant.jpg',
  })
  avatar?: string;

  /**
   * Prompt hoặc hướng dẫn cho agent con
   */
  @ApiProperty({
    description: 'Prompt hoặc hướng dẫn cho agent con',
    example: 'Bạn là trợ lý chuyên về marketing, hãy giúp tạo nội dung quảng cáo hiệu quả',
  })
  prompt: string;
}

/**
 * DTO cho kết quả bulk operation với Multi-Agent
 */
export class BulkMultiAgentOperationResponseDto {
  /**
   * Danh sách ID agent con thành công
   */
  @ApiProperty({
    description: 'Danh sách ID agent con thành công',
    example: ['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002'],
    type: [String],
  })
  idSuccess: string[];

  /**
   * Danh sách ID agent con thất bại
   */
  @ApiProperty({
    description: 'Danh sách ID agent con thất bại',
    example: ['550e8400-e29b-41d4-a716-446655440003'],
    type: [String],
  })
  idFailed: string[];

  /**
   * Chi tiết lỗi cho từng ID thất bại
   */
  @ApiProperty({
    description: 'Chi tiết lỗi cho từng ID thất bại',
    example: { 
      '550e8400-e29b-41d4-a716-446655440003': 'Agent không tồn tại hoặc không thuộc về user' 
    },
    type: 'object',
    additionalProperties: { type: 'string' },
  })
  errors: Record<string, string>;

  /**
   * Tổng số items được xử lý
   */
  @ApiProperty({
    description: 'Tổng số items được xử lý',
    example: 3,
  })
  totalProcessed: number;

  /**
   * Số items thành công
   */
  @ApiProperty({
    description: 'Số items thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số items thất bại
   */
  @ApiProperty({
    description: 'Số items thất bại',
    example: 1,
  })
  failedCount: number;
}
