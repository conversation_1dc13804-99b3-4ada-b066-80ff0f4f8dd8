import { ApiProperty } from '@nestjs/swagger';
import { UserAgentRunStatus } from '../../../shared/enums';
import { ModificationDetails } from '../interfaces/modify-result.interface';

/**
 * DTO for chat message response
 *
 * @example Regular Message Response
 * {
 *   "messageId": "0280c24b-c849-492d-b5fd-e1c927186272",
 *   "runId": "run_123456-789-abc",
 *   "agentName": "Customer Support Agent",
 *   "status": "created",
 *   "createdAt": 1672531200000
 * }
 *
 * @example Modify-Only Request Response
 * {
 *   "messageId": "msg_123_modified",
 *   "runId": "run_456",
 *   "agentName": "Assistant",
 *   "status": "created",
 *   "createdAt": 1749708424552,
 *   "modificationDetails": {
 *     "modifiedMessageId": "msg_123",
 *     "deletedMessageIds": ["msg_124", "msg_125"],
 *     "deletedMessagesCount": 2,
 *     "operationType": "modify_text"
 *   }
 * }
 *
 * @example Mixed Request Response (Modify + New Content)
 * {
 *   "messageId": "msg_new_456",
 *   "runId": "run_789",
 *   "agentName": "Assistant",
 *   "status": "created",
 *   "createdAt": 1749708424552,
 *   "modificationDetails": {
 *     "modifiedMessageId": "msg_123",
 *     "deletedMessageIds": ["msg_124"],
 *     "deletedMessagesCount": 1,
 *     "operationType": "modify_text"
 *   }
 * }
 */
export class MessageResponseDto {

  @ApiProperty({
    description: 'ID of the created message',
    example: '0280c24b-c849-492d-b5fd-e1c927186272'
  })
  messageId: string

  @ApiProperty({
    description: 'ID of the created run',
    example: 'run_123456-789-abc'
  })
  runId: string;

  @ApiProperty({
    description: 'Name of the agent processing the message',
    example: 'Customer Support Agent'
  })
  agentName: string;

  @ApiProperty({
    description: 'Current status of the run',
    enum: UserAgentRunStatus,
    example: UserAgentRunStatus.CREATED
  })
  status: UserAgentRunStatus;

  @ApiProperty({
    description: 'Timestamp when the run was created',
    example: 1672531200000
  })
  createdAt: number;

  @ApiProperty({
    description: `Details about message modifications (only present for modify operations).

    When a user modifies a message, this field contains:
    - modifiedMessageId: ID of the message that was modified
    - deletedMessageIds: Array of message IDs that were deleted due to cascade delete
    - deletedMessagesCount: Total count of messages deleted
    - operationType: Type of modification performed

    Cascade Delete Behavior:
    When a message is modified, ALL subsequent messages (both user and assistant)
    in the conversation thread are automatically deleted to maintain conversation consistency.

    Examples:
    - Modify-only request: Only modificationDetails present, no new message created
    - Mixed request: Both new message and modification details present`,
    required: false,
    examples: {
      'Modify Only': {
        value: {
          modifiedMessageId: 'msg_123',
          deletedMessageIds: ['msg_124', 'msg_125'],
          deletedMessagesCount: 2,
          operationType: 'modify_text'
        }
      },
      'Mixed Request': {
        value: {
          modifiedMessageId: 'msg_456',
          deletedMessageIds: ['msg_457'],
          deletedMessagesCount: 1,
          operationType: 'modify_text'
        }
      },
      'No Deletions': {
        value: {
          modifiedMessageId: 'msg_789',
          deletedMessageIds: [],
          deletedMessagesCount: 0,
          operationType: 'modify_text'
        }
      }
    }
  })
  modificationDetails?: ModificationDetails;

  constructor(data: {
    messageId: string;
    runId: string;
    agentName: string;
    status: UserAgentRunStatus;
    createdAt: number;
    modificationDetails?: ModificationDetails;
  }) {
    this.messageId = data.messageId;
    this.runId = data.runId;
    this.agentName = data.agentName;
    this.status = data.status;
    this.createdAt = data.createdAt;
    this.modificationDetails = data.modificationDetails;
  }
}
