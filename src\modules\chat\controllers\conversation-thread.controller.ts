import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { AppException, ErrorCode } from '@common/exceptions';
import { ConversationThreadService } from '../services/conversation-thread.service';
import {
  ConversationThreadResponseDto,
  CreateConversationThreadDto,
  GetConversationThreadsQueryDto,
  GetThreadMessagesQueryDto,
  ThreadMessageResponseDto,
  UpdateConversationThreadDto,
} from '../dto/conversation-thread.dto';
import { MessageRequestDto } from '../dto/message-request.dto';
import { CHAT_ERROR_CODES } from '../exceptions';
import { DeleteThreadsDto } from '../dto/delete-threads.dto';

@ApiTags('Conversation Threads')
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ConversationThreadResponseDto,
  ThreadMessageResponseDto,
  CreateConversationThreadDto,
  UpdateConversationThreadDto,
  GetConversationThreadsQueryDto,
  GetThreadMessagesQueryDto,
  MessageRequestDto,
)
@Controller('user/chat/threads')
export class ConversationThreadController {
  constructor(
    private readonly conversationThreadService: ConversationThreadService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new conversation thread',
    description: 'Creates a new conversation thread for the authenticated user',
  })
  @ApiCreatedResponse({
    description: 'Conversation thread created successfully',
    schema: ApiResponseDto.getSchema(ConversationThreadResponseDto),
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
    CHAT_ERROR_CODES.THREAD_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateConversationThreadDto,
  ) {
    const thread = await this.conversationThreadService.create(
      createDto,
      user.id,
    );
    return ApiResponseDto.created(
      thread,
      'Conversation thread created successfully',
    );
  }

  @Get()
  @ApiOperation({
    summary: 'Get all conversation threads for the user',
    description:
      'Retrieves all conversation threads belonging to the authenticated user with pagination and search',
  })
  @ApiResponse({
    status: 200,
    description: 'Conversation threads retrieved successfully',
    schema: ApiResponseDto.getPaginatedSchema(ConversationThreadResponseDto),
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: GetConversationThreadsQueryDto,
  ) {
    const threads = await this.conversationThreadService.findAllByUser(
      queryDto,
      user.id,
    );
    return ApiResponseDto.success(
      threads,
      'Conversation threads retrieved successfully',
    );
  }

  @Get(':threadId')
  @ApiOperation({
    summary: 'Get a specific conversation thread',
    description:
      'Retrieves a specific conversation thread by ID for the authenticated user',
  })
  @ApiParam({
    name: 'threadId',
    description: 'UUID of the conversation thread',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @ApiResponse({
    status: 200,
    description: 'Conversation thread retrieved successfully',
    schema: ApiResponseDto.getSchema(ConversationThreadResponseDto),
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_NOT_FOUND,
    CHAT_ERROR_CODES.THREAD_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param(
      'threadId',
      new ParseUUIDPipe({
        exceptionFactory: () => {
          throw new AppException(
            CHAT_ERROR_CODES.INVALID_INPUT,
            'threadId must be a valid UUID',
          );
        },
      }),
    )
    threadId: string,
  ) {
    const thread = await this.conversationThreadService.findOne(
      threadId,
      user.id,
    );
    return ApiResponseDto.success(
      thread,
      'Conversation thread retrieved successfully',
    );
  }

  @Put(':threadId')
  @ApiOperation({
    summary: 'Update a conversation thread',
    description:
      'Updates a specific conversation thread for the authenticated user',
  })
  @ApiParam({
    name: 'threadId',
    description: 'UUID of the conversation thread',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @ApiOkResponse({
    description: 'Conversation thread updated successfully',
    schema: ApiResponseDto.getSchema(ConversationThreadResponseDto),
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_NOT_FOUND,
    CHAT_ERROR_CODES.THREAD_UPDATE_FAILED,
    CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiBody({ type: UpdateConversationThreadDto })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param(
      'threadId',
      new ParseUUIDPipe({
        exceptionFactory: () => {
          throw new AppException(
            CHAT_ERROR_CODES.INVALID_INPUT,
            'threadId must be a valid UUID',
          );
        },
      }),
    )
    threadId: string,
    @Body() updateDto: UpdateConversationThreadDto,
  ) {
    const thread = await this.conversationThreadService.update(
      threadId,
      user.id,
      updateDto,
    );
    return ApiResponseDto.success(
      thread,
      'Conversation thread updated successfully',
    );
  }

  @Get(':threadId/messages')
  @ApiOperation({
    summary: 'Get messages in a conversation thread',
    description:
      'Retrieves all messages in a specific conversation thread with pagination and filtering',
  })
  @ApiParam({
    name: 'threadId',
    description: 'UUID of the conversation thread',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @ApiResponse({
    status: 200,
    description: 'Thread messages retrieved successfully',
    schema: ApiResponseDto.getPaginatedSchema(ThreadMessageResponseDto),
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_NOT_FOUND,
    CHAT_ERROR_CODES.MESSAGES_FETCH_FAILED,
    CHAT_ERROR_CODES.MESSAGE_FORMAT_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getThreadMessages(
    @CurrentUser() user: JwtPayload,
    @Param(
      'threadId',
      new ParseUUIDPipe({
        exceptionFactory: () => {
          throw new AppException(
            CHAT_ERROR_CODES.INVALID_INPUT,
            'threadId must be a valid UUID',
          );
        },
      }),
    )
    threadId: string,
    @Query() queryDto: GetThreadMessagesQueryDto,
  ) : Promise<ApiResponseDto<PaginatedResult<ThreadMessageResponseDto>>> {
    const messages = await this.conversationThreadService.getThreadMessages(
      threadId,
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      messages,
      'Thread messages retrieved successfully',
    );
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete multiple conversation threads',
    description:
      'Deletes one or more conversation threads for the authenticated user based on a list of IDs.',
  })
  @ApiBody({ type: DeleteThreadsDto }) // Document the expected request body
  @ApiOkResponse({
    description: 'Conversation threads deleted successfully',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_NOT_FOUND,
    CHAT_ERROR_CODES.THREAD_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async remove(
    @CurrentUser() user: JwtPayload,
    @Body() deleteThreadsDto: DeleteThreadsDto, // Use the DTO and validation pipe
  ) {
    await this.conversationThreadService.remove(deleteThreadsDto.threadIds, user.id);
    return ApiResponseDto.success(
      null,
      'Conversation thread deleted successfully',
    );
  }
}
