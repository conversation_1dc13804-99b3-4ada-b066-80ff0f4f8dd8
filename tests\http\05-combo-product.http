### Test Combo Product Creation
### Tạ<PERSON> sản phẩm combo với đầy đủ thông tin

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Combo khóa học lập trình Full-stack",
  "productType": "COMBO",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 3000000,
    "salePrice": 2500000,
    "currency": "VND"
  },
  "description": "Combo khóa học lập trình Full-stack bao gồm Frontend, Backend và Database",
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["combo", "lập trình", "fullstack", "khóa học"],
  "purchaseCount": 0,
  "comboItems": [
    {
      "productId": 1,
      "productName": "Khóa học Frontend React",
      "quantity": 1,
      "originalPrice": 1200000,
      "discountedPrice": 1000000,
      "metadata": {
        "duration": "2 months",
        "level": "Beginner to Intermediate"
      }
    },
    {
      "productId": 2,
      "productName": "<PERSON>h<PERSON><PERSON> học Backend Node.js",
      "quantity": 1,
      "originalPrice": 1500000,
      "discountedPrice": 1200000,
      "metadata": {
        "duration": "2.5 months",
        "level": "Intermediate"
      }
    },
    {
      "productId": 3,
      "productName": "Khóa học Database MongoDB",
      "quantity": 1,
      "originalPrice": 800000,
      "discountedPrice": 600000,
      "metadata": {
        "duration": "1 month",
        "level": "Beginner"
      }
    }
  ],
  "classifications": [
    {
      "name": "Gói học sinh viên",
      "price": {
        "listPrice": 2500000,
        "salePrice": 2000000,
        "currency": "VND"
      },
      "description": "Gói ưu đãi cho sinh viên",
      "metadata": {
        "discount": "20%",
        "requirement": "Student ID",
        "support": "6 months"
      }
    },
    {
      "name": "Gói người đi làm",
      "price": {
        "listPrice": 3000000,
        "salePrice": 2500000,
        "currency": "VND"
      },
      "description": "Gói cho người đi làm",
      "metadata": {
        "discount": "17%",
        "support": "12 months",
        "certificate": "Professional"
      }
    }
  ]
}

### Test Combo Product với giá phân loại

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Combo thiết bị văn phòng",
  "productType": "COMBO",
  "typePrice": "CLASSIFICATION_PRICE",
  "description": "Combo thiết bị văn phòng hoàn chỉnh cho startup",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["combo", "văn phòng", "thiết bị", "startup"],
  "purchaseCount": 0,
  "comboItems": [
    {
      "productId": 10,
      "productName": "Laptop Dell Inspiron",
      "quantity": 1,
      "originalPrice": 15000000,
      "discountedPrice": 14000000,
      "metadata": {
        "specs": "i5, 8GB RAM, 256GB SSD",
        "warranty": "2 years"
      }
    },
    {
      "productId": 11,
      "productName": "Màn hình 24 inch",
      "quantity": 1,
      "originalPrice": 3000000,
      "discountedPrice": 2800000,
      "metadata": {
        "resolution": "1920x1080",
        "panel": "IPS"
      }
    },
    {
      "productId": 12,
      "productName": "Bàn phím cơ",
      "quantity": 1,
      "originalPrice": 1500000,
      "discountedPrice": 1300000,
      "metadata": {
        "switch": "Cherry MX Blue",
        "backlight": "RGB"
      }
    }
  ],
  "classifications": [
    {
      "name": "Combo cơ bản",
      "price": {
        "listPrice": 18000000,
        "salePrice": 16500000,
        "currency": "VND"
      },
      "description": "Combo cơ bản cho 1 người",
      "metadata": {
        "users": "1",
        "setup": "Basic",
        "support": "3 months"
      }
    },
    {
      "name": "Combo team nhỏ",
      "price": {
        "listPrice": 50000000,
        "salePrice": 45000000,
        "currency": "VND"
      },
      "description": "Combo cho team 3 người",
      "metadata": {
        "users": "3",
        "setup": "Team",
        "support": "6 months"
      }
    },
    {
      "name": "Combo startup",
      "price": {
        "listPrice": 80000000,
        "salePrice": 70000000,
        "currency": "VND"
      },
      "description": "Combo cho startup 5 người",
      "metadata": {
        "users": "5",
        "setup": "Professional",
        "support": "12 months"
      }
    }
  ]
}

### Test Combo Product đơn giản

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Combo sách lập trình",
  "productType": "COMBO",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 800000,
    "salePrice": 600000,
    "currency": "VND"
  },
  "description": "Combo 3 cuốn sách lập trình best-seller",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["combo", "sách", "lập trình", "ebook"],
  "purchaseCount": 0,
  "comboItems": [
    {
      "productId": 20,
      "productName": "Clean Code",
      "quantity": 1,
      "originalPrice": 300000,
      "discountedPrice": 250000,
      "metadata": {
        "author": "Robert C. Martin",
        "pages": "464",
        "format": "PDF"
      }
    },
    {
      "productId": 21,
      "productName": "Design Patterns",
      "quantity": 1,
      "originalPrice": 350000,
      "discountedPrice": 280000,
      "metadata": {
        "author": "Gang of Four",
        "pages": "395",
        "format": "PDF"
      }
    },
    {
      "productId": 22,
      "productName": "Refactoring",
      "quantity": 1,
      "originalPrice": 320000,
      "discountedPrice": 260000,
      "metadata": {
        "author": "Martin Fowler",
        "pages": "448",
        "format": "PDF"
      }
    }
  ]
}

### Test Combo Product với số lượng khác nhau

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Combo áo thun gia đình",
  "productType": "COMBO",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 1200000,
    "salePrice": 1000000,
    "currency": "VND"
  },
  "description": "Combo áo thun cho cả gia đình 4 người",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["combo", "áo thun", "gia đình", "thời trang"],
  "purchaseCount": 0,
  "comboItems": [
    {
      "productId": 30,
      "productName": "Áo thun nam",
      "quantity": 2,
      "originalPrice": 300000,
      "discountedPrice": 250000,
      "metadata": {
        "sizes": "L, XL",
        "colors": "Trắng, Đen"
      }
    },
    {
      "productId": 31,
      "productName": "Áo thun nữ",
      "quantity": 1,
      "originalPrice": 280000,
      "discountedPrice": 230000,
      "metadata": {
        "size": "M",
        "color": "Hồng"
      }
    },
    {
      "productId": 32,
      "productName": "Áo thun trẻ em",
      "quantity": 1,
      "originalPrice": 200000,
      "discountedPrice": 170000,
      "metadata": {
        "size": "S",
        "color": "Xanh"
      }
    }
  ]
}

### Test Combo Product với dịch vụ

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Combo website + hosting + domain",
  "productType": "COMBO",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 6000000,
    "salePrice": 5000000,
    "currency": "VND"
  },
  "description": "Combo hoàn chỉnh để có website chuyên nghiệp",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["combo", "website", "hosting", "domain"],
  "purchaseCount": 0,
  "comboItems": [
    {
      "productId": 40,
      "productName": "Thiết kế website",
      "quantity": 1,
      "originalPrice": 4000000,
      "discountedPrice": 3500000,
      "metadata": {
        "type": "Service",
        "pages": "5",
        "timeline": "2 weeks"
      }
    },
    {
      "productId": 41,
      "productName": "Hosting 1 năm",
      "quantity": 1,
      "originalPrice": 1200000,
      "discountedPrice": 1000000,
      "metadata": {
        "type": "Service",
        "storage": "10GB",
        "bandwidth": "Unlimited"
      }
    },
    {
      "productId": 42,
      "productName": "Domain .com",
      "quantity": 1,
      "originalPrice": 500000,
      "discountedPrice": 400000,
      "metadata": {
        "type": "Service",
        "duration": "1 year",
        "extension": ".com"
      }
    }
  ]
}
