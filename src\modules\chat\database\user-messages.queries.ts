import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from './database.service';
// ✅ REMOVED: MessageRequestDto import - no longer used since content doesn't contain full DTO

/**
 * ✅ UPDATED: Message content without threadId (threadId is stored as separate column)
 */
export interface MessageContentWithoutThreadId {
  contentBlocks: any[]; // Content blocks from MessageRequestDto
  alwaysApproveToolCall?: boolean; // Tool call setting
  attachmentContext?: any[]; // Attachment context
  // ✅ REMOVED: threadId - stored as separate database column, not in JSONB content
}

/**
 * ✅ UPDATED: Interface for user message data (content no longer contains threadId)
 */
export interface UserMessageData {
  message_id?: string;
  thread_id: string; // ✅ threadId from database column
  role: 'user' | 'assistant';
  content: MessageContentWithoutThreadId; // ✅ JSONB content without redundant threadId
  timestamp?: number;
  created_by: number;
}

/**
 * ✅ UPDATED: Interface for creating user messages (content no longer contains threadId)
 */
export interface CreateUserMessageData {
  thread_id: string; // ✅ threadId for database column
  role: 'user' | 'assistant';
  content: MessageContentWithoutThreadId; // ✅ Content without redundant threadId
  created_by: number;
}

/**
 * User Messages Queries Service
 * 
 * Handles database operations for the user_messages table using raw SQL queries.
 * This service is used to persist user messages and AI responses for frontend display.
 */
@Injectable()
export class UserMessagesQueries {
  private readonly logger = new Logger(UserMessagesQueries.name);

  constructor(private readonly databaseService: ChatDatabaseService) {}

  /**
   * Create a new user message
   * @param messageData Message data to create
   * @returns Promise<string> Created message ID
   */
  async createMessage(messageData: CreateUserMessageData): Promise<string> {
    const query = `
      INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING message_id
    `;

    const timestamp = Date.now();
    const values = [
      messageData.thread_id,
      messageData.role,
      JSON.stringify(messageData.content),
      timestamp,
      messageData.created_by
    ];

    try {
      this.logger.debug(`Creating message for thread ${messageData.thread_id}, role: ${messageData.role}`);
      
      const result = await this.databaseService.query(query, values);
      const messageId = result[0]?.message_id;

      if (!messageId) {
        throw new Error('Failed to create message - no ID returned');
      }

      this.logger.log(`Created message ${messageId} for thread ${messageData.thread_id}`);
      return messageId;

    } catch (error) {
      this.logger.error(`Failed to create message for thread ${messageData.thread_id}:`, error);
      throw new Error(`Message creation failed: ${error.message}`);
    }
  }

  /**
   * Get messages by thread ID
   * @param threadId Thread ID to get messages for
   * @param limit Optional limit for number of messages
   * @returns Promise<UserMessageData[]> Array of messages
   */
  async getMessagesByThreadId(threadId: string, limit?: number): Promise<UserMessageData[]> {
    let query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE thread_id = $1
      ORDER BY timestamp ASC
    `;

    const values: any[] = [threadId];

    if (limit) {
      query += ` LIMIT $2`;
      values.push(limit);
    }

    try {
      this.logger.debug(`Getting messages for thread ${threadId}${limit ? ` (limit: ${limit})` : ''}`);
      
      const result = await this.databaseService.query(query, values);

      const messages = result.map(row => ({
        message_id: row.message_id,
        thread_id: row.thread_id,
        role: row.role,
        content: row.content, // Already parsed by PostgreSQL
        timestamp: row.timestamp,
        created_by: row.created_by
      }));

      this.logger.debug(`Found ${messages.length} messages for thread ${threadId}`);
      return messages;

    } catch (error) {
      this.logger.error(`Failed to get messages for thread ${threadId}:`, error);
      throw new Error(`Failed to retrieve messages: ${error.message}`);
    }
  }

  /**
   * Get a specific message by ID
   * @param messageId Message ID to get
   * @returns Promise<UserMessageData | null> Message data or null if not found
   */
  async getMessageById(messageId: string): Promise<UserMessageData | null> {
    const query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE message_id = $1
    `;

    try {
      this.logger.debug(`Getting message ${messageId}`);
      
      const result = await this.databaseService.query(query, [messageId]);

      if (result.length === 0) {
        this.logger.debug(`Message ${messageId} not found`);
        return null;
      }

      const row = result[0];
      return {
        message_id: row.message_id,
        thread_id: row.thread_id,
        role: row.role,
        content: row.content,
        timestamp: row.timestamp,
        created_by: row.created_by
      };

    } catch (error) {
      this.logger.error(`Failed to get message ${messageId}:`, error);
      throw new Error(`Failed to retrieve message: ${error.message}`);
    }
  }

  /**
   * Get message count by thread ID
   * @param threadId Thread ID to count messages for
   * @returns Promise<number> Number of messages
   */
  async getMessageCountByThreadId(threadId: string): Promise<number> {
    const query = `
      SELECT COUNT(*) as count
      FROM user_messages
      WHERE thread_id = $1
    `;

    try {
      const result = await this.databaseService.query(query, [threadId]);
      const count = parseInt(result[0]?.count || '0');

      this.logger.debug(`Thread ${threadId} has ${count} messages`);
      return count;

    } catch (error) {
      this.logger.error(`Failed to count messages for thread ${threadId}:`, error);
      throw new Error(`Message count failed: ${error.message}`);
    }
  }

  /**
   * Delete messages by thread ID
   * @param threadId Thread ID to delete messages for
   * @returns Promise<boolean> True if deletion was successful
   */
  async deleteMessagesByThreadId(threadId: string): Promise<boolean> {
    const query = `
      DELETE FROM user_messages
      WHERE thread_id = $1
    `;

    try {
      this.logger.debug(`Deleting messages for thread ${threadId}`);

      await this.databaseService.query(query, [threadId]);

      this.logger.log(`Deleted messages for thread ${threadId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to delete messages for thread ${threadId}:`, error);
      throw new Error(`Message deletion failed: ${error.message}`);
    }
  }
}
