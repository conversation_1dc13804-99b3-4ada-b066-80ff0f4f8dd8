import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsUUID,
  IsOptional,
  IsString,
  IsArray,
} from 'class-validator';

/**
 * File attachment context DTO - simplified like content blocks
 */
export class FileAttachmentContextDto {
  @ApiProperty({
    description: "The type of attachment, fixed to 'file'.",
    enum: ['file'],
    example: 'file',
  })
  @IsEnum(['file'])
  @IsNotEmpty()
  type: 'file';

  @ApiProperty({
    description: 'File ID - must be a valid UUID format',
    example: 'a9b1c1d8-e3f4-5a6b-7c8d-9e0f1a2b3c4d',
  })
  @IsUUID(4, { message: 'fileId must be a valid UUID' })
  @IsNotEmpty()
  fileId: string;

  @ApiProperty({
    description: 'The display name of the file.',
    example: 'Invoice-April.pdf',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'tags of the image',
    example: ['funny', 'cute'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[] = [];

  @ApiProperty({
    description: 'Optional description of the file.',
    example: 'This is the invoice for April.',
    required: false,
  })
  @IsOptional()
  @IsString()
  desc?: string;
}

/**
 * Image attachment context DTO - simplified like content blocks
 */
export class ImageAttachmentContextDto {
  @ApiProperty({
    description: "The type of attachment, fixed to 'image'.",
    enum: ['image'],
    example: 'image',
  })
  @IsEnum(['image'])
  @IsNotEmpty()
  type: 'image';

  @ApiProperty({
    description: 'Image ID - must be a valid UUID format',
    example: 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
  })
  @IsUUID(4, { message: 'fileId must be a valid UUID' })
  @IsNotEmpty()
  fileId: string;

  @ApiProperty({
    description: 'The display name of the image.',
    example: 'Profile Photo',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'path of the image',
    example: '/path/to/image.png',
  })
  @IsString()
  @IsNotEmpty()
  path: string;

  @ApiProperty({
    description: 'tags of the image',
    example: ['funny', 'cute'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[] = [];

  @ApiProperty({
    description: 'Optional description of the image.',
    example: 'Uploaded by user as a profile picture.',
    required: false,
  })
  @IsOptional()
  @IsString()
  desc?: string;
}

/**
 * Union type for attachment context
 */
export type AttachmentContextDto =
  | FileAttachmentContextDto
  | ImageAttachmentContextDto;

/**
 * Array of attachment contexts for multiple attachments
 */
export type AttachmentContextArrayDto = AttachmentContextDto[];
