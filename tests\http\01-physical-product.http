### Test Physical Product Creation
### Tạo sản phẩm vật lý với đầy đủ thông tin

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "<PERSON><PERSON> thun nam cao cấp",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  },
  "description": "<PERSON>o thun nam chất liệu cotton 100% cao cấp, tho<PERSON><PERSON> mát, thấm hút mồ hôi tốt",
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["thời trang", "nam", "áo thun", "cotton"],
  "shipmentConfig": {
    "widthCm": 25,
    "heightCm": 5,
    "lengthCm": 30,
    "weightGram": 200
  },
  "inventory": {
    "availableQuantity": 100,
    "sku": "SHIRT-MEN-001",
    "barcode": "1234567890123"
  },
  "classifications": [
    {
      "name": "Size S",
      "price": {
        "listPrice": 280000,
        "salePrice": 230000,
        "currency": "VND"
      },
      "description": "Size S cho người từ 50-55kg",
      "metadata": {
        "size": "S",
        "color": "Trắng"
      },
      "images": []
    },
    {
      "name": "Size M",
      "price": {
        "listPrice": 300000,
        "salePrice": 250000,
        "currency": "VND"
      },
      "description": "Size M cho người từ 55-65kg",
      "metadata": {
        "size": "M",
        "color": "Đen"
      },
      "images": []
    }
  ]
}

### Test Physical Product với inventory có sẵn
### Sử dụng inventoryId thay vì tạo mới

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Quần jean nam",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 500000,
    "salePrice": 450000,
    "currency": "VND"
  },
  "description": "Quần jean nam form slim fit",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["thời trang", "nam", "quần jean"],
  "shipmentConfig": {
    "widthCm": 30,
    "heightCm": 3,
    "lengthCm": 35,
    "weightGram": 400
  },
  "inventoryId": 1
}

### Test Physical Product không có inventory
### Chỉ tạo sản phẩm cơ bản

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Giày thể thao",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 800000,
    "salePrice": 700000,
    "currency": "VND"
  },
  "description": "Giày thể thao nam nữ đa năng",
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["giày", "thể thao", "unisex"],
  "shipmentConfig": {
    "widthCm": 35,
    "heightCm": 15,
    "lengthCm": 25,
    "weightGram": 800
  }
}

### Test Physical Product với warehouse cụ thể

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Túi xách nữ",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 600000,
    "salePrice": 500000,
    "currency": "VND"
  },
  "description": "Túi xách nữ da thật cao cấp",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["túi xách", "nữ", "da thật"],
  "shipmentConfig": {
    "widthCm": 40,
    "heightCm": 10,
    "lengthCm": 30,
    "weightGram": 500
  },
  "inventory": {
    "availableQuantity": 50,
    "sku": "BAG-WOMEN-001",
    "barcode": "9876543210987",
    "warehouseId": 1
  }
}

### Test Physical Product với giá phân loại

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Đồng hồ thông minh",
  "productType": "PHYSICAL",
  "typePrice": "CLASSIFICATION_PRICE",
  "description": "Đồng hồ thông minh đa chức năng",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["đồng hồ", "thông minh", "công nghệ"],
  "shipmentConfig": {
    "widthCm": 15,
    "heightCm": 5,
    "lengthCm": 20,
    "weightGram": 100
  },
  "classifications": [
    {
      "name": "Bản 42mm",
      "price": {
        "listPrice": 2000000,
        "salePrice": 1800000,
        "currency": "VND"
      },
      "description": "Đồng hồ 42mm cho nam",
      "metadata": {
        "size": "42mm",
        "gender": "Nam"
      }
    },
    {
      "name": "Bản 38mm",
      "price": {
        "listPrice": 1800000,
        "salePrice": 1600000,
        "currency": "VND"
      },
      "description": "Đồng hồ 38mm cho nữ",
      "metadata": {
        "size": "38mm",
        "gender": "Nữ"
      }
    }
  ]
}
