import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize, ArrayMaxSize, ArrayUnique } from 'class-validator';

/**
 * DTO cho việc thêm nhiều tools vào agent
 */
export class AddAgentToolsDto {
  /**
   * <PERSON><PERSON> sách ID của các tools cần thêm vào agent
   */
  @ApiProperty({
    description: 'Danh sách ID của các tools cần thêm vào agent (tối thiểu 1, tối đa 50)',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************'
    ],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray({ message: 'toolIds phải là một mảng' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 tool' })
  @ArrayMaxSize(50, { message: '<PERSON><PERSON><PERSON><PERSON> được vượt quá 50 tools' })
  @ArrayUnique({ message: 'Không được có tool ID trùng lặp' })
  @IsUUID('4', { each: true, message: 'Mỗi tool ID phải là UUID hợp lệ' })
  toolIds: string[];
}
