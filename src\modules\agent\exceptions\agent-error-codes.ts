import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';

/**
 * Agent Error Codes (Range: 10100-10199)
 */
export const AGENT_ERROR_CODES = {
  AGENT_NOT_FOUND: new ErrorCode(10100, 'Không tìm thấy agent', HttpStatus.NOT_FOUND),
  AGENT_ALREADY_EXISTS: new ErrorCode(10101, 'Agent đã tồn tại', HttpStatus.CONFLICT),
  INVALID_AGENT_CONFIG: new ErrorCode(10102, 'Cấu hình agent không hợp lệ', HttpStatus.BAD_REQUEST),
  AGENT_CREATION_FAILED: new ErrorCode(10103, 'Tạo agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  AGENT_UPDATE_FAILED: new ErrorCode(10104, '<PERSON><PERSON>p nhật agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  AGENT_DELETE_FAILED: new ErrorCode(10105, 'Xóa agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  INSUFFICIENT_PERMISSIONS: new ErrorCode(10106, 'Không đủ quyền truy cập', HttpStatus.FORBIDDEN),
  AGENT_LIST_QUERY_FAILED: new ErrorCode(10107, 'Truy vấn danh sách agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),

  // Model Resolution Error Codes (10108-10121)
  INVALID_KEY_LLM_ID: new ErrorCode(10108, 'Key LLM ID không hợp lệ hoặc không tồn tại', HttpStatus.BAD_REQUEST),
  MODEL_REGISTRY_NOT_FOUND: new ErrorCode(10109, 'Không tìm thấy model registry phù hợp', HttpStatus.NOT_FOUND),
  MODEL_BASE_NOT_FOUND: new ErrorCode(10110, 'Không tìm thấy model base active', HttpStatus.NOT_FOUND),
  MODEL_PROVIDER_MISMATCH: new ErrorCode(10111, 'Provider của model và key LLM không khớp', HttpStatus.BAD_REQUEST),
  MODEL_NOT_ACCESSIBLE: new ErrorCode(10112, 'Model không được phép truy cập', HttpStatus.FORBIDDEN),
  INVALID_MODEL_NAME_FORMAT: new ErrorCode(10113, 'Định dạng tên model không hợp lệ', HttpStatus.BAD_REQUEST),
  MODEL_RESOLUTION_FAILED: new ErrorCode(10114, 'Không thể resolve model', HttpStatus.INTERNAL_SERVER_ERROR),
  CANNOT_DELETE_ACTIVE_AGENT: new ErrorCode(10115, 'Không thể xóa agent đang active', HttpStatus.BAD_REQUEST),

  // Integration Error Codes (10116-10121)
  FACEBOOK_PAGE_NOT_FOUND: new ErrorCode(10116, 'Không tìm thấy Facebook page', HttpStatus.NOT_FOUND),
  WEBSITE_NOT_FOUND: new ErrorCode(10117, 'Không tìm thấy website', HttpStatus.NOT_FOUND),

  // MCP Systems Error Codes (10122-10131)
  MCP_SYSTEM_NOT_FOUND: new ErrorCode(10122, 'Không tìm thấy MCP system', HttpStatus.NOT_FOUND),
  MCP_SYSTEM_NAME_EXISTS: new ErrorCode(10123, 'Tên server MCP đã tồn tại', HttpStatus.CONFLICT),
  MCP_SYSTEM_CREATION_FAILED: new ErrorCode(10124, 'Tạo MCP system thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  MCP_SYSTEM_UPDATE_FAILED: new ErrorCode(10125, 'Cập nhật MCP system thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  MCP_SYSTEM_DELETE_FAILED: new ErrorCode(10126, 'Xóa MCP system thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_MCP_CONFIG: new ErrorCode(10127, 'Cấu hình MCP không hợp lệ', HttpStatus.BAD_REQUEST),

  // Multi-Agent Error Codes (10132-10141)
  MULTI_AGENT_NOT_FOUND: new ErrorCode(10132, 'Không tìm thấy quan hệ multi-agent', HttpStatus.NOT_FOUND),
  MULTI_AGENT_SELF_REFERENCE: new ErrorCode(10133, 'Agent không thể tham chiếu đến chính mình', HttpStatus.BAD_REQUEST),
  MULTI_AGENT_CREATION_FAILED: new ErrorCode(10134, 'Tạo quan hệ multi-agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  MULTI_AGENT_DELETE_FAILED: new ErrorCode(10135, 'Xóa quan hệ multi-agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_MULTI_AGENT_CONFIG: new ErrorCode(10136, 'Cấu hình multi-agent không hợp lệ', HttpStatus.BAD_REQUEST),

  // Agent Tools Error Codes (10142-10151)
  AGENT_TOOLS_NOT_FOUND: new ErrorCode(10142, 'Không tìm thấy tools của agent', HttpStatus.NOT_FOUND),
  AGENT_TOOLS_ADD_FAILED: new ErrorCode(10143, 'Thêm tools vào agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  AGENT_TOOLS_REMOVE_FAILED: new ErrorCode(10144, 'Gỡ tools khỏi agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  AGENT_TOOLS_QUERY_FAILED: new ErrorCode(10145, 'Truy vấn tools của agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  INVALID_TOOL_IDS: new ErrorCode(10146, 'Danh sách tool IDs không hợp lệ', HttpStatus.BAD_REQUEST),
  TOOLS_NOT_FOUND: new ErrorCode(10147, 'Không tìm thấy tools được chỉ định', HttpStatus.NOT_FOUND),
  TOOLS_ALREADY_ASSIGNED: new ErrorCode(10148, 'Một số tools đã được gán cho agent', HttpStatus.CONFLICT),
};
