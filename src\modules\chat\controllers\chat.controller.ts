import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import {
  Controller,
  UseGuards,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Param,
  Delete,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import {
  Api<PERSON>earerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiExtraModels,
  ApiBody,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ChatService } from '../services/chat.service';
import { MessageRequestDto, MessageRequestExamples } from '../dto/message-request.dto';
import { MessageResponseDto } from '../dto/message-response.dto';
import { ApiResponseDto } from '@/common/response';
import { TextContentBlockDto } from '../dto/text-content-block.dto';
import { ImageContentBlockDto } from '../dto/image-content-block.dto';
import { FileContentBlockDto } from '../dto/file-content-block.dto';
import { ToolCallDecisionDto } from '../dto/tool-call-decision.dto';
import {
  FileAttachmentContextDto,
  ImageAttachmentContextDto,
} from '../dto/attachment-context.dto';

/**
 * Controller for handling chat endpoints
 */
@ApiTags('Chat')
@Controller('/user/chat')
@ApiExtraModels(
  ApiResponseDto,
  MessageResponseDto,
  MessageRequestDto,
  TextContentBlockDto,
  ImageContentBlockDto,
  FileContentBlockDto,
  ToolCallDecisionDto,
  FileAttachmentContextDto,
  ImageAttachmentContextDto,
)
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  /**
   * Send a message to an agent and create a run for processing
   * @param userId ID of the user sending the message
   * @param messageRequest Message request data
   * @returns Information about the created run
   */
  @Post('message')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Send message to agent',
    description: `Send a message to a specific agent and create a run for processing. The agent will process the message asynchronously.

    Supports multiple content types:
    - Text messages
    - Image and file attachments
    - Reply to previous messages (using replyToMessageId field)
    - Modify last text message (with cascade delete)

    Reply Message Behavior:
    Use the replyToMessageId field at the message level to reply to previous messages. This provides a clean, flat structure instead of nested content blocks.

    Modify Message Behavior:
    When using modify_last_text, all subsequent messages in the conversation are automatically deleted to maintain consistency.`,
  })
  @ApiResponse({
    status: 201,
    description: 'Message sent successfully and run created',
    schema: ApiResponseDto.getSchema(MessageResponseDto),
    examples: {
      'Regular Message Response': {
        summary: 'Standard message response',
        value: {
          success: true,
          message: 'Message sent successfully',
          result: {
            messageId: '0280c24b-c849-492d-b5fd-e1c927186272',
            runId: 'run_123456-789-abc',
            agentName: 'Customer Support Agent',
            status: 'created',
            createdAt: 1672531200000,
            modificationDetails: {
              modifiedMessageId: '',
              deletedMessageIds: [],
              deletedMessagesCount: 0,
            },
          },
        },
      },
      'Modify-Only Response': {
        summary: 'Response for modify-only request',
        value: {
          success: true,
          message: 'Message modified successfully',
          result: {
            messageId: 'msg_123_modified',
            runId: 'run_456',
            agentName: 'Assistant',
            status: 'created',
            createdAt: 1749708424552,
            modificationDetails: {
              modifiedMessageId: 'msg_123',
              deletedMessageIds: ['msg_124', 'msg_125'],
              deletedMessagesCount: 2,
            },
          },
        },
      },
      'Mixed Request Response': {
        summary: 'Response for modify + new content request',
        value: {
          success: true,
          message: 'Message sent successfully',
          result: {
            messageId: 'msg_new_456',
            runId: 'run_789',
            agentName: 'Assistant',
            status: 'created',
            createdAt: 1749708424552,
            modificationDetails: {
              modifiedMessageId: 'msg_123',
              deletedMessageIds: ['msg_124'],
              deletedMessagesCount: 1,
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description:
      'Invalid request data, agent not found, or validation failed (e.g., trying to modify message with no text content)',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - valid JWT token required',
  })
  @ApiBody({
    type: MessageRequestDto,
    examples: MessageRequestExamples, // <-- SEE HOW CLEAN THIS IS!
  })
  async sendMessage(
    @CurrentUser('id') userId: number,
    @Body() messageRequest: MessageRequestDto,
    @Req() request: Request,
  ): Promise<ApiResponseDto<MessageResponseDto>> {
    // Extract JWT token from Authorization header
    const authHeader = request.headers.authorization;
    const jwt = authHeader?.startsWith('Bearer ')
      ? authHeader.substring(7)
      : '';

    return ApiResponseDto.created(
      await this.chatService.processMessage(messageRequest, userId, jwt),
    );
  }

  /**
   * Cancel a running or pending run
   * @param runId ID of the run to cancel
   * @returns Cancellation result
   */
  @Delete('runs/:runId')
  @HttpCode(HttpStatus.OK)
  @ApiParam({
    name: 'runId',
    description: 'ID of the run to cancel',
    example: 'run_123456-789-abc',
  })
  @ApiOperation({
    summary: 'Cancel a run',
    description:
      'Cancel a running or pending run. This will stop processing and mark the run as failed.',
  })
  @ApiResponse({
    status: 200,
    description: 'Run cancelled successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Run cancelled successfully' },
        runId: { type: 'string', example: 'run_123456-789-abc' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Run not found or already completed',
  })
  async cancelRun(@Param('runId') runId: string): Promise<object> {
    const success = await this.chatService.cancelRun(runId);

    return {
      success,
      message: success ? 'Run cancelled successfully' : 'Failed to cancel run',
      runId,
    };
  }
}
