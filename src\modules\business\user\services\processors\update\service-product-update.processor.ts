import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto } from '../../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm dịch vụ (SERVICE)
 * Xử lý service packages, service metadata, duration, provider
 */
@Injectable()
export class ServiceProductUpdateProcessor {
  private readonly logger = new Logger(ServiceProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
  ) {}

  /**
   * Cập nhật sản phẩm dịch vụ hoàn chỉnh
   */
  @Transactional()
  async updateServiceProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    _userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating SERVICE product: ${product.name} (ID: ${product.id}) for user: ${_userId}`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho dịch vụ
    this.validateServiceProductData(updateDto);

    // BƯỚC 2: Cập nhật service-specific fields
    this.updateServiceFields(product, updateDto);

    // BƯỚC 3: Xử lý service metadata
    this.updateServiceMetadata(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho dịch vụ
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý service packages trong advanced info
    await this.processServicePackagesUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Service products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho dịch vụ
   */
  private validateServiceProductData(
    updateDto: BusinessUpdateProductDto,
  ): void {
    const serviceAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    // Validate purchase count nếu có
    if (
      serviceAdvancedInfo?.purchaseCount !== undefined &&
      (serviceAdvancedInfo.purchaseCount as number) < 0
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }

    // Validate service packages nếu có trong advanced info
    if (serviceAdvancedInfo?.servicePackages) {
      this.validateServicePackages(
        serviceAdvancedInfo.servicePackages as unknown[],
      );
    }

    // Validate service metadata fields
    this.validateServiceMetadata(updateDto);
  }

  /**
   * Validate service packages
   */
  private validateServicePackages(servicePackages: unknown[]): void {
    if (!Array.isArray(servicePackages)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Service packages phải là một mảng',
      );
    }

    for (const servicePackage of servicePackages) {
      const packageObj = servicePackage as Record<string, unknown>;

      // Validate required fields
      if (!packageObj.name) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service package phải có tên',
        );
      }

      // Validate price nếu có
      if (packageObj.price) {
        const price = packageObj.price as Record<string, unknown>;
        if (!price.listPrice || (price.listPrice as number) < 0) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Giá service package không hợp lệ',
          );
        }
      }

      // Validate duration format nếu có
      if (packageObj.duration && typeof packageObj.duration !== 'string') {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Duration phải là chuỗi ký tự',
        );
      }

      // Validate features nếu có
      if (packageObj.features && !Array.isArray(packageObj.features)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Features phải là một mảng',
        );
      }
    }
  }

  /**
   * Validate service metadata
   */
  private validateServiceMetadata(updateDto: BusinessUpdateProductDto): void {
    const serviceAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (serviceAdvancedInfo) {
      // Validate service time format
      if (
        serviceAdvancedInfo.serviceTime &&
        typeof serviceAdvancedInfo.serviceTime !== 'string'
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service time phải là chuỗi ký tự',
        );
      }

      // Validate service duration format
      if (
        serviceAdvancedInfo.serviceDuration &&
        typeof serviceAdvancedInfo.serviceDuration !== 'string'
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service duration phải là chuỗi ký tự',
        );
      }

      // Validate service provider
      if (
        serviceAdvancedInfo.serviceProvider &&
        typeof serviceAdvancedInfo.serviceProvider !== 'string'
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service provider phải là chuỗi ký tự',
        );
      }

      // Validate service type
      if (
        serviceAdvancedInfo.serviceType &&
        typeof serviceAdvancedInfo.serviceType !== 'string'
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service type phải là chuỗi ký tự',
        );
      }

      // Validate service location
      if (
        serviceAdvancedInfo.serviceLocation &&
        typeof serviceAdvancedInfo.serviceLocation !== 'string'
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Service location phải là chuỗi ký tự',
        );
      }
    }
  }

  /**
   * Cập nhật các trường đặc thù cho dịch vụ
   */
  private updateServiceFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const serviceAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    // Cập nhật purchase count
    if (serviceAdvancedInfo?.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount =
        serviceAdvancedInfo.purchaseCount as number;
      this.logger.log(
        `Updated purchase count to: ${serviceAdvancedInfo.purchaseCount as number}`,
      );
    }
  }

  /**
   * Cập nhật service metadata
   */
  private updateServiceMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const serviceAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (serviceAdvancedInfo) {
      product.metadata = product.metadata || {};

      // Cập nhật service time
      if (serviceAdvancedInfo.serviceTime !== undefined) {
        product.metadata.serviceTime =
          serviceAdvancedInfo.serviceTime as string;
        this.logger.log(
          `Updated service time to: ${serviceAdvancedInfo.serviceTime as string}`,
        );
      }

      // Cập nhật service duration
      if (serviceAdvancedInfo.serviceDuration !== undefined) {
        product.metadata.serviceDuration =
          serviceAdvancedInfo.serviceDuration as string;
        this.logger.log(
          `Updated service duration to: ${serviceAdvancedInfo.serviceDuration as string}`,
        );
      }

      // Cập nhật service provider
      if (serviceAdvancedInfo.serviceProvider !== undefined) {
        product.metadata.serviceProvider =
          serviceAdvancedInfo.serviceProvider as string;
        this.logger.log(
          `Updated service provider to: ${serviceAdvancedInfo.serviceProvider as string}`,
        );
      }

      // Cập nhật service type
      if (serviceAdvancedInfo.serviceType !== undefined) {
        product.metadata.serviceType =
          serviceAdvancedInfo.serviceType as string;
        this.logger.log(
          `Updated service type to: ${serviceAdvancedInfo.serviceType as string}`,
        );
      }

      // Cập nhật service location
      if (serviceAdvancedInfo.serviceLocation !== undefined) {
        product.metadata.serviceLocation =
          serviceAdvancedInfo.serviceLocation as string;
        this.logger.log(
          `Updated service location to: ${serviceAdvancedInfo.serviceLocation as string}`,
        );
      }
    }
  }

  /**
   * Đảm bảo shipment config = 0 cho dịch vụ
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for SERVICE product');
  }

  /**
   * Xử lý cập nhật service packages trong advanced info
   */
  private async processServicePackagesUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const serviceAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!serviceAdvancedInfo?.servicePackages || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(
        `Updating service packages for SERVICE product ${product.id}`,
      );

      // Tìm advanced info hiện tại
      const existingAdvancedInfo =
        await this.productAdvancedInfoRepository.findOne({
          where: { id: product.detail_id },
        });

      if (existingAdvancedInfo) {
        // Xử lý service packages
        const processedServicePackages = (
          serviceAdvancedInfo.servicePackages as unknown[]
        ).map((servicePackage: unknown) => {
          const packageObj = servicePackage as Record<string, unknown>;
          // Loại bỏ imagesMediaTypes nếu có
          const { imagesMediaTypes, ...packageWithoutImages } = packageObj;
          // Sử dụng imagesMediaTypes để tránh lỗi unused variable
          void imagesMediaTypes;
          return packageWithoutImages;
        });

        // Cập nhật service packages trong advanced info
        existingAdvancedInfo.servicePackages =
          processedServicePackages as never;
        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(
          `Successfully updated service packages for SERVICE product ${product.id}`,
        );
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error updating service packages for SERVICE product ${product.id}: ${errorMessage}`,
        errorStack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật service packages: ${errorMessage}`,
      );
    }
  }
}
