import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiResponse,
} from '@nestjs/swagger';
import { UserPhysicalWarehouseService } from '@modules/business/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import {
  CreatePhysicalWarehouseDto,
  CreatePhysicalWarehouseWithDetailsDto,
  UpdatePhysicalWarehouseDto,
  PhysicalWarehouseResponseDto,
  QueryPhysicalWarehouseDto,
  BulkDeletePhysicalWarehouseDto,
  BulkDeletePhysicalWarehouseResponseDto,
} from '../dto/warehouse';
import { ApiErrorResponse } from '@/common/error/api-error-response.decorator';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý các request liên quan đến kho vật lý của người dùng
 */
@ApiTags('User Physical Warehouse')
@Controller('user/physical-warehouses')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserPhysicalWarehouseController {
  constructor(private readonly userPhysicalWarehouseService: UserPhysicalWarehouseService) {}

  /**
   * Lấy danh sách kho vật lý với phân trang và lọc
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kho vật lý với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kho vật lý với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(PhysicalWarehouseResponseDto),
  })
  async getPhysicalWarehouses(
    @Query() queryDto: QueryPhysicalWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    queryDto.userId = user.id;
    const physicalWarehouses = await this.userPhysicalWarehouseService.getPhysicalWarehouses(queryDto);
    return ApiResponseDto.success(physicalWarehouses, 'Lấy danh sách kho vật lý thành công');
  }

  /**
   * Tạo mới kho vật lý
   * @param createDto DTO chứa thông tin tạo kho vật lý mới
   * @param user
   * @returns Thông tin kho vật lý đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Tạo mới kho vật lý' })
  @ApiCreatedResponse({
    description: 'Tạo mới kho vật lý thành công',
    type: () => ApiResponseDto.getSchema(PhysicalWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_VALIDATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: CreatePhysicalWarehouseDto })
  async createPhysicalWarehouse(
    @Body() createDto: CreatePhysicalWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    createDto.userId = user.id;
    const physicalWarehouse = await this.userPhysicalWarehouseService.createPhysicalWarehouse(createDto);
    return ApiResponseDto.created<PhysicalWarehouseResponseDto>(physicalWarehouse, 'Tạo mới kho vật lý thành công');
  }

  /**
   * Tạo mới kho vật lý kèm thông tin chi tiết (gộp tạo warehouse và physical warehouse)
   * @param createDto DTO chứa thông tin tạo kho vật lý kèm chi tiết
   * @param user
   * @returns Thông tin kho vật lý đã tạo
   */
  @Post('create/new')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Tạo mới kho vật lý kèm thông tin chi tiết' })
  @ApiCreatedResponse({
    description: 'Tạo mới kho vật lý kèm thông tin chi tiết thành công',
    type: () => ApiResponseDto.getSchema(PhysicalWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_CREATION_FAILED,
    BUSINESS_ERROR_CODES.WAREHOUSE_VALIDATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: CreatePhysicalWarehouseWithDetailsDto })
  async createPhysicalWarehouseWithDetails(
    @Body() createDto: CreatePhysicalWarehouseWithDetailsDto,
    @CurrentUser() user: JwtPayload
  ) {
    const physicalWarehouse = await this.userPhysicalWarehouseService.createPhysicalWarehouseWithDetails(createDto, user.id);
    return ApiResponseDto.created<PhysicalWarehouseResponseDto>(physicalWarehouse, 'Tạo mới kho vật lý kèm thông tin chi tiết thành công');
  }

  /**
   * Xóa nhiều kho vật lý
   * @param bulkDeleteDto DTO chứa danh sách ID kho cần xóa
   * @param user
   * @returns Kết quả xóa nhiều kho vật lý
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa nhiều kho vật lý' })
  @ApiOkResponse({
    description: 'Xóa nhiều kho vật lý thành công',
    type: () => ApiResponseDto.getSchema(BulkDeletePhysicalWarehouseResponseDto),
  })
  @ApiResponse({
    status: 207,
    description: 'Một số kho vật lý không thể xóa',
    type: () => ApiResponseDto.getSchema(BulkDeletePhysicalWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: BulkDeletePhysicalWarehouseDto })
  async bulkDeletePhysicalWarehouses(
    @Body() bulkDeleteDto: BulkDeletePhysicalWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    const result = await this.userPhysicalWarehouseService.bulkDeletePhysicalWarehouses(bulkDeleteDto, user.id);

    return ApiResponseDto.success(result, result.message);
  }

  /**
   * Lấy thông tin kho vật lý theo ID
   * @param warehouseId ID của kho
   * @param user
   * @returns Thông tin chi tiết của kho vật lý
   */
  @Get(':warehouseId')
  @ApiOperation({ summary: 'Lấy thông tin kho vật lý theo ID' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiOkResponse({
    description: 'Lấy thông tin kho vật lý thành công',
    type: () => ApiResponseDto.getSchema(PhysicalWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
    BUSINESS_ERROR_CODES.WAREHOUSE_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getPhysicalWarehouseById(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @CurrentUser() user: JwtPayload
  ) {
    const physicalWarehouse = await this.userPhysicalWarehouseService.getPhysicalWarehouseById(warehouseId, user.id);
    return ApiResponseDto.success<PhysicalWarehouseResponseDto>(physicalWarehouse, 'Lấy thông tin kho vật lý thành công');
  }

  /**
   * Cập nhật thông tin kho vật lý
   * @param warehouseId ID của kho
   * @param updateDto DTO chứa thông tin cập nhật
   * @param user
   * @returns Thông tin kho vật lý đã cập nhật
   */
  @Put(':warehouseId')
  @ApiOperation({ summary: 'Cập nhật thông tin kho vật lý' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiOkResponse({
    description: 'Cập nhật kho vật lý thành công',
    type: () => ApiResponseDto.getSchema(PhysicalWarehouseResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
    BUSINESS_ERROR_CODES.WAREHOUSE_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ 
    type: UpdatePhysicalWarehouseDto,
    description: 'Thông tin cập nhật kho vật lý (tên kho, mô tả, địa chỉ, sức chứa)'
  })
  async updatePhysicalWarehouse(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @Body() updateDto: UpdatePhysicalWarehouseDto,
    @CurrentUser() user: JwtPayload
  ) {
    const physicalWarehouse = await this.userPhysicalWarehouseService.updatePhysicalWarehouse(warehouseId, updateDto, user.id);
    return ApiResponseDto.success<PhysicalWarehouseResponseDto>(physicalWarehouse, 'Cập nhật kho vật lý thành công');
  }

  /**
   * Xóa kho vật lý
   * @param warehouseId ID của kho
   * @param user
   * @returns Thông báo kết quả xóa
   */
  @Delete(':warehouseId')
  @ApiOperation({ summary: 'Xóa kho vật lý' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', type: 'number' })
  @ApiOkResponse({
    description: 'Xóa kho vật lý thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    BUSINESS_ERROR_CODES.WAREHOUSE_TYPE_MISMATCH,
    BUSINESS_ERROR_CODES.WAREHOUSE_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deletePhysicalWarehouse(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
    @CurrentUser() user: JwtPayload
  ) {
    const result = await this.userPhysicalWarehouseService.deletePhysicalWarehouse(warehouseId, user.id);
    return ApiResponseDto.success(result, result.message);
  }

}
