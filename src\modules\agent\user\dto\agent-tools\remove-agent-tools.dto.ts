import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize, ArrayMaxSize, ArrayUnique } from 'class-validator';

/**
 * DTO cho việc gỡ bỏ nhiều tools khỏi agent
 */
export class RemoveAgentToolsDto {
  /**
   * <PERSON>h sách ID của các tools cần gỡ bỏ khỏi agent
   */
  @ApiProperty({
    description: 'Danh sách ID của các tools cần gỡ bỏ khỏi agent (tối thiểu 1, tối đa 50)',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************'
    ],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray({ message: 'toolIds phải là một mảng' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 tool' })
  @ArrayMaxSize(50, { message: '<PERSON>hông được vượt quá 50 tools' })
  @ArrayUnique({ message: 'Không được có tool ID trùng lặp' })
  @IsUUID('4', { each: true, message: 'Mỗi tool ID phải là UUID hợp lệ' })
  toolIds: string[];
}
