### Test Digital Product Creation
### Tạ<PERSON> sản phẩm số với đầy đủ thông tin

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "<PERSON>h<PERSON><PERSON> học lập trình Python",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 1000000,
    "salePrice": 800000,
    "currency": "VND"
  },
  "description": "Khóa học lập trình <PERSON> từ cơ bản đến nâng cao, bao gồm thực hành dự án thực tế",
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["lập trình", "python", "khóa học", "online"],
  "deliveryMethod": "EMAIL",
  "deliveryTiming": "IMMEDIATE",
  "outputType": "ACCESS_CODE",
  "purchaseCount": 0,
  "classifications": [
    {
      "name": "<PERSON><PERSON>i cơ bản",
      "price": {
        "listPrice": 800000,
        "salePrice": 600000,
        "currency": "VND"
      },
      "description": "Gói học cơ bản 3 tháng",
      "metadata": {
        "duration": "3 tháng",
        "support": "Email",
        "certificate": "Có"
      },
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "customFields": {
        "accessLevel": "basic",
        "features": ["Video lessons", "PDF materials"]
      },
      "images": []
    },
    {
      "name": "Gói nâng cao",
      "price": {
        "listPrice": 1200000,
        "salePrice": 1000000,
        "currency": "VND"
      },
      "description": "Gói học nâng cao 6 tháng",
      "metadata": {
        "duration": "6 tháng",
        "support": "1-on-1 mentoring",
        "certificate": "Có"
      },
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "customFields": {
        "accessLevel": "advanced",
        "features": ["Video lessons", "PDF materials", "Live sessions", "Project review"]
      },
      "images": []
    }
  ]
}

### Test Digital Product với delivery qua SMS

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Ebook Marketing Digital",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 200000,
    "salePrice": 150000,
    "currency": "VND"
  },
  "description": "Sách điện tử về marketing digital cho doanh nghiệp nhỏ",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["ebook", "marketing", "digital", "kinh doanh"],
  "deliveryMethod": "SMS",
  "deliveryTiming": "DELAYED",
  "outputType": "DOWNLOAD_LINK",
  "purchaseCount": 0
}

### Test Digital Product với giá phân loại

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Template thiết kế Figma",
  "productType": "DIGITAL",
  "typePrice": "CLASSIFICATION_PRICE",
  "description": "Bộ template thiết kế UI/UX chuyên nghiệp cho Figma",
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["template", "figma", "ui", "ux", "thiết kế"],
  "deliveryMethod": "DASHBOARD",
  "deliveryTiming": "IMMEDIATE",
  "outputType": "CONTENT",
  "purchaseCount": 0,
  "classifications": [
    {
      "name": "Template Mobile App",
      "price": {
        "listPrice": 500000,
        "salePrice": 400000,
        "currency": "VND"
      },
      "description": "Template thiết kế mobile app",
      "metadata": {
        "category": "Mobile",
        "screens": "20+",
        "format": "Figma"
      }
    },
    {
      "name": "Template Website",
      "price": {
        "listPrice": 700000,
        "salePrice": 600000,
        "currency": "VND"
      },
      "description": "Template thiết kế website",
      "metadata": {
        "category": "Web",
        "pages": "15+",
        "format": "Figma"
      }
    }
  ]
}

### Test Digital Product với auto active

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Phần mềm quản lý bán hàng",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 2000000,
    "salePrice": 1500000,
    "currency": "VND"
  },
  "description": "Phần mềm quản lý bán hàng cho cửa hàng nhỏ",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["phần mềm", "quản lý", "bán hàng", "pos"],
  "deliveryMethod": "AUTO_ACTIVE",
  "deliveryTiming": "IMMEDIATE",
  "outputType": "ACCOUNT_INFO",
  "purchaseCount": 0
}

### Test Digital Product với Zalo delivery

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Video hướng dẫn nấu ăn",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  },
  "description": "Bộ video hướng dẫn nấu các món ăn Việt Nam truyền thống",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["video", "nấu ăn", "món việt", "hướng dẫn"],
  "deliveryMethod": "ZALO",
  "deliveryTiming": "IMMEDIATE",
  "outputType": "DOWNLOAD_LINK",
  "purchaseCount": 0
}

### Test Digital Product với Direct Message

POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Preset Lightroom chuyên nghiệp",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 400000,
    "salePrice": 300000,
    "currency": "VND"
  },
  "description": "Bộ preset Lightroom cho nhiếp ảnh chân dung và phong cảnh",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["preset", "lightroom", "nhiếp ảnh", "chỉnh sửa"],
  "deliveryMethod": "DIRECT_MESSAGE",
  "deliveryTiming": "DELAYED",
  "outputType": "DOWNLOAD_LINK",
  "purchaseCount": 0
}
