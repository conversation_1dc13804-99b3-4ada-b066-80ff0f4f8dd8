import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { MetadataFieldResponseDto } from './metadata-field.dto';

/**
 * DTO cho thông tin agent
 */
export class AgentInfoDto {
  @Expose()
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @Expose()
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Agent hỗ trợ khách hàng',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện của agent',
    example: 'https://example.com/agent-avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho thông tin user
 */
export class UserInfoDto {
  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON>n đ<PERSON>y đủ của người dùng',
    example: 'Nguyễn Văn <PERSON>',
    nullable: true,
  })
  fullName: string;

  @Expose()
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
    nullable: true,
  })
  email: string;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0912345678',
    nullable: true,
  })
  phoneNumber: string;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện của người dùng',
    example: 'https://example.com/user-avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho response khi lấy thông tin khách hàng chuyển đổi
 */
export class UserConvertCustomerResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string;

  @Expose()
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Email khách hàng',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    nullable: true,
  })
  email: any;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0912345678',
    nullable: true,
  })
  phone: string;

  @Expose()
  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    nullable: true,
  })
  platform: string;

  @Expose()
  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    nullable: true,
  })
  timezone: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng sở hữu khách hàng',
    example: 1001,
    nullable: true,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  agentId: string;

  @Expose()
  @ApiProperty({
    description: 'Thông tin chi tiết về agent hỗ trợ khách hàng',
    type: AgentInfoDto,
    nullable: true,
  })
  @Type(() => AgentInfoDto)
  agent?: AgentInfoDto;

  @Expose()
  @ApiProperty({
    description: 'Thông tin chi tiết về người dùng sở hữu khách hàng',
    type: UserInfoDto,
    nullable: true,
  })
  @Type(() => UserInfoDto)
  user?: UserInfoDto;

  @Expose()
  @ApiProperty({
    description: 'Trường tùy chỉnh với configId và giá trị',
    type: [MetadataFieldResponseDto],
    example: [
      {
        configId: 'day_of_birth',
        value: '28/11/2003',
        label: 'date_of_birth',
        type: 'text',
        required: true
      },
      {
        configId: 'haianh',
        value: 'Thông tin bổ sung',
        label: 'haianh',
        type: 'text',
        required: false
      },
      {
        configId: 'sâs',
        value: 'Nguyễn Văn A',
        label: 'Name',
        type: 'text',
        required: true
      }
    ],
  })
  @Type(() => MetadataFieldResponseDto)
  metadata: MetadataFieldResponseDto[];

  /**
   * Link Facebook của khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Link Facebook của khách hàng',
    example: 'https://facebook.com/user123',
    nullable: true,
  })
  facebookLink: string;

  /**
   * Link Twitter của khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Link Twitter của khách hàng',
    example: 'https://twitter.com/user123',
    nullable: true,
  })
  twitterLink: string;

  /**
   * Link LinkedIn của khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Link LinkedIn của khách hàng',
    example: 'https://linkedin.com/in/user123',
    nullable: true,
  })
  linkedinLink: string;

  /**
   * Link Zalo của khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Link Zalo của khách hàng',
    example: 'https://zalo.me/user123',
    nullable: true,
  })
  zaloLink: string;

  /**
   * Link Website của khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Link Website của khách hàng',
    example: 'https://example.com',
    nullable: true,
  })
  websiteLink: string;

  /**
   * Địa chỉ khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    nullable: true,
  })
  address: string | null;

  /**
   * Thông tin upload avatar (nếu có)
   */
  @Expose()
  @ApiProperty({
    description: 'Thông tin upload avatar',
    type: 'object',
    properties: {
      uploadUrl: {
        type: 'string',
        description: 'URL để upload avatar',
        example: 'https://s3.amazonaws.com/bucket/path/to/file?signature=...'
      },
      key: {
        type: 'string',
        description: 'S3 key của file avatar',
        example: 'customer_avatars/2024/01/user_123/1641708800000-uuid.jpg'
      },
      expiresAt: {
        type: 'number',
        description: 'Thời gian hết hạn của presigned URL',
        example: 1641708800000
      }
    },
  })
  avatarUpload?: {
    uploadUrl: string;
    key: string;
    expiresAt: number;
  };

  @Expose()
  @ApiProperty({
    description: 'Danh sách tag/nhãn cho khách hàng',
    example: ['VIP', 'Potential', 'Hot Lead'],
    required: false,
    type: [String],
  })
  tags?: string[];
}

/**
 * DTO cho danh sách khách hàng chuyển đổi
 */
export class UserConvertCustomerListItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string;

  @Expose()
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Email khách hàng',
    example: { primary: '<EMAIL>' },
    nullable: true,
  })
  email: any;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0912345678',
    nullable: true,
  })
  phone: string;

  @Expose()
  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    nullable: true,
  })
  platform: string;

  @Expose()
  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    nullable: true,
  })
  timezone: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1741708800000,
  })
  updatedAt: number;


  /**
   * Địa chỉ khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    nullable: true,
  })
  address: string | null;

  @Expose()
  @ApiProperty({
    description: 'Trường tùy chỉnh với configId và giá trị',
    type: [MetadataFieldResponseDto],
    example: [
      {
        configId: 'day_of_birth',
        value: '28/11/2003',
        label: 'date_of_birth',
        type: 'text',
        required: true
      },
      {
        configId: 'haianh',
        value: 'Thông tin bổ sung',
        label: 'haianh',
        type: 'text',
        required: false
      }
    ],
    nullable: true,
  })
  @Type(() => MetadataFieldResponseDto)
  metadata: MetadataFieldResponseDto[];
}
