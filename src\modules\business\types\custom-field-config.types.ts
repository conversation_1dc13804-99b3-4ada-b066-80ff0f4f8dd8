/**
 * Types cho configJson của Custom Field Entity trong Business Module
 * Dựa trên phân tích các API và DTO thực tế
 */

// ==================== TYPES ====================

/**
 * Type cho các loại dữ liệu của custom field
 * Dựa trên TypeCustomField từ business module
 */
export type TypeCustomField = 'text' | 'number' | 'boolean' | 'date' | 'select' | 'object' | 'array';

/**
 * Enum cho các loại dữ liệu của custom field (để tương thích)
 */
export enum CustomFieldDataType {
  TEXT = 'text',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  SELECT = 'select',
  OBJECT = 'object',
  ARRAY = 'array',
}

// ==================== BASE INTERFACES ====================

/**
 * Base interface cho tất cả config types
 * Dựa trên cấu trúc thực tế từ business module
 */
export interface BaseCustomFieldConfig {
  /**
   * Placeholder hiển thị trong input
   */
  placeholder?: string;

  /**
   * Mô tả về trường
   */
  description?: string;

  /**
   * ID duy nhất của trường (từ create-custom-field.json example)
   */
  id?: string;

  /**
   * Label hiển thị (từ create-custom-field.json example)
   */
  label?: string;

  /**
   * Loại component UI (text, select, checkbox, etc.)
   */
  type?: string;

  /**
   * Trường bắt buộc hay không
   */
  required?: boolean;

  /**
   * Variant của component (outlined, filled, standard)
   */
  variant?: 'outlined' | 'filled' | 'standard';

  /**
   * Kích thước component (small, medium, large)
   */
  size?: 'small' | 'medium' | 'large';
}

/**
 * Interface cho validation config
 * Dựa trên ValidationDto từ admin/dto/customfields/custom-field-config.dto.ts
 */
export interface ValidationConfig {
  /**
   * Mẫu regex để kiểm tra
   */
  pattern?: string;

  /**
   * Độ dài tối thiểu
   */
  minLength?: number;

  /**
   * Độ dài tối đa
   */
  maxLength?: number;

  /**
   * Giá trị tối thiểu (cho number)
   */
  minValue?: number;

  /**
   * Giá trị tối đa (cho number)
   */
  maxValue?: number;

  /**
   * Thông báo lỗi tùy chỉnh
   */
  errorMessage?: string;
}

/**
 * Interface cho option trong select
 * Dựa trên OptionDto từ admin/dto/customfields/custom-field-config.dto.ts
 * và example từ create-custom-field.json
 */
export interface SelectOption {
  /**
   * Nhãn hiển thị của tùy chọn
   */
  label?: string;

  /**
   * Giá trị thực tế (hỗ trợ string, number, boolean)
   */
  value: any;
}

// ==================== SPECIFIC CONFIG INTERFACES ====================

/**
 * Interface cho config của dataType = 'text'
 */
export interface TextCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: string;
  
  /**
   * Mẫu kiểm tra pattern (regex)
   */
  pattern?: string;
  
  /**
   * Độ dài tối thiểu
   */
  minLength?: number;
  
  /**
   * Độ dài tối đa
   */
  maxLength?: number;
  
  /**
   * Cấu hình validation (cho hệ thống cũ)
   */
  validation?: ValidationConfig;
}

/**
 * Interface cho config của dataType = 'number'
 */
export interface NumberCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: number;
  
  /**
   * Giá trị tối thiểu
   */
  minValue?: number;
  
  /**
   * Giá trị tối đa
   */
  maxValue?: number;
  
  /**
   * Cấu hình validation (cho hệ thống cũ)
   */
  validation?: ValidationConfig;
}

/**
 * Interface cho config của dataType = 'boolean'
 */
export interface BooleanCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: boolean;
}

/**
 * Interface cho config của dataType = 'date'
 */
export interface DateCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định (ISO date string)
   */
  defaultValue?: string;
  
  /**
   * Định dạng ngày hiển thị
   */
  format?: string;
  
  /**
   * Ngày tối thiểu
   */
  minDate?: string;
  
  /**
   * Ngày tối đa
   */
  maxDate?: string;
}

/**
 * Interface cho config của dataType = 'select'
 */
export interface SelectCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Danh sách các tùy chọn
   */
  options: SelectOption[];
  
  /**
   * Giá trị mặc định (phải là một trong các value trong options)
   */
  defaultValue?: string | number | boolean;
  
  /**
   * Cho phép chọn nhiều hay không
   */
  multiple?: boolean;
}

/**
 * Interface cho config của dataType = 'object'
 */
export interface ObjectCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: Record<string, any>;

  /**
   * Schema định nghĩa cấu trúc object
   */
  schema?: Record<string, any>;
}

/**
 * Interface cho config của dataType = 'array'
 */
export interface ArrayCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Giá trị mặc định
   */
  defaultValue?: any[];

  /**
   * Loại dữ liệu của các phần tử trong array
   */
  itemType?: TypeCustomField;

  /**
   * Schema cho các phần tử trong array
   */
  itemSchema?: Record<string, any>;

  /**
   * Số lượng phần tử tối thiểu
   */
  minItems?: number;

  /**
   * Số lượng phần tử tối đa
   */
  maxItems?: number;
}

// ==================== BUSINESS MODULE CONFIG INTERFACE ====================

/**
 * Interface cho config của business module
 * Dựa trên cấu trúc thực tế từ các DTO và examples
 * Hỗ trợ cấu trúc linh hoạt với validation object
 */
export interface BusinessCustomFieldConfig extends BaseCustomFieldConfig {
  /**
   * Cấu hình validation (từ ValidationDto)
   */
  validation?: ValidationConfig;

  /**
   * Giá trị mặc định (hỗ trợ any type)
   */
  defaultValue?: any;

  /**
   * Danh sách options (cho select/dropdown)
   * Có thể là array string đơn giản hoặc array object
   */
  options?: string[] | SelectOption[];

  /**
   * Độ dài tối đa (shorthand, ngoài validation)
   */
  maxLength?: number;

  /**
   * Các thuộc tính tùy chỉnh khác
   */
  additionalProperties?: Record<string, any>;
}

// ==================== UNION TYPES ====================

/**
 * Union type cho tất cả các config types theo dataType
 */
export type TypedCustomFieldConfig =
  | TextCustomFieldConfig
  | NumberCustomFieldConfig
  | BooleanCustomFieldConfig
  | DateCustomFieldConfig
  | SelectCustomFieldConfig
  | ObjectCustomFieldConfig
  | ArrayCustomFieldConfig;

/**
 * Union type cho configJson - Type chính cho business module
 * Hỗ trợ cả cấu trúc typed và flexible
 */
export type CustomFieldConfigJson =
  | TypedCustomFieldConfig
  | BusinessCustomFieldConfig
  | Record<string, any>; // Fallback cho các trường hợp đặc biệt

// ==================== TYPE GUARDS ====================

/**
 * Type guard để kiểm tra config có phải là text config không
 */
export const isTextConfig = (config: any): config is TextCustomFieldConfig => {
  return typeof config === 'object' && config !== null && 
    (config.pattern !== undefined || config.minLength !== undefined || config.maxLength !== undefined);
};

/**
 * Type guard để kiểm tra config có phải là number config không
 */
export const isNumberConfig = (config: any): config is NumberCustomFieldConfig => {
  return typeof config === 'object' && config !== null && 
    (config.minValue !== undefined || config.maxValue !== undefined);
};

/**
 * Type guard để kiểm tra config có phải là select config không
 */
export const isSelectConfig = (config: any): config is SelectCustomFieldConfig => {
  return typeof config === 'object' && config !== null && Array.isArray(config.options);
};

/**
 * Type guard để kiểm tra config có phải là array config không
 */
export const isArrayConfig = (config: any): config is ArrayCustomFieldConfig => {
  return typeof config === 'object' && config !== null &&
    (config.itemType !== undefined || config.minItems !== undefined || config.maxItems !== undefined);
};

/**
 * Type guard để kiểm tra config có phải là business config không
 */
export const isBusinessConfig = (config: any): config is BusinessCustomFieldConfig => {
  return typeof config === 'object' && config !== null &&
    (config.validation !== undefined || config.id !== undefined || config.label !== undefined);
};

// ==================== UTILITY TYPES ====================

/**
 * Type để extract dataType từ config
 */
export type ExtractDataType<T> =
  T extends TextCustomFieldConfig ? CustomFieldDataType.TEXT :
  T extends NumberCustomFieldConfig ? CustomFieldDataType.NUMBER :
  T extends BooleanCustomFieldConfig ? CustomFieldDataType.BOOLEAN :
  T extends DateCustomFieldConfig ? CustomFieldDataType.DATE :
  T extends SelectCustomFieldConfig ? CustomFieldDataType.SELECT :
  T extends ObjectCustomFieldConfig ? CustomFieldDataType.OBJECT :
  T extends ArrayCustomFieldConfig ? CustomFieldDataType.ARRAY :
  never;

/**
 * Type để lấy config theo dataType
 */
export type ConfigByDataType<T extends CustomFieldDataType> =
  T extends CustomFieldDataType.TEXT ? TextCustomFieldConfig :
  T extends CustomFieldDataType.NUMBER ? NumberCustomFieldConfig :
  T extends CustomFieldDataType.BOOLEAN ? BooleanCustomFieldConfig :
  T extends CustomFieldDataType.DATE ? DateCustomFieldConfig :
  T extends CustomFieldDataType.SELECT ? SelectCustomFieldConfig :
  T extends CustomFieldDataType.OBJECT ? ObjectCustomFieldConfig :
  T extends CustomFieldDataType.ARRAY ? ArrayCustomFieldConfig :
  never;
