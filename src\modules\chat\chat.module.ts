import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ChatService } from './services/chat.service';
import { AgentConfigService } from './services/agent-config.service';
import { ConversationThreadService } from './services/conversation-thread.service';
import { ChatController } from './controllers/chat.controller';
import { StreamController } from './controllers/stream.controller';
import { ConversationThreadController } from './controllers/conversation-thread.controller';
import { ChatDatabaseService, UserAgentRunsQueries, AgentConfigQueries, UserMessagesQueries } from './database';
import { ConfigService } from '../../config';
import { ConfigType } from '../../config/constants';
import { RedisConfig } from '../../config/interfaces';
import {
  Agent,
  AgentSystem,
  AgentUser,
  UserMultiAgent
} from '@modules/agent/entities';
import { UserConversationThread } from './entities/user-conversation-thread.entity';
import { ImageService } from './services/image.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Agent,
      AgentSystem,
      AgentUser,
      UserMultiAgent,
      UserConversationThread,
    ]),
    ClientsModule.registerAsync([
      {
        name: 'REDIS_CLIENT',
        useFactory: (configService: ConfigService) => {
          const redisConfig = configService.getConfig<RedisConfig>(ConfigType.Redis);

          // Parse Redis URL to extract host, port, and other options
          const url = new URL(redisConfig.url);

          return {
            transport: Transport.REDIS,
            options: {
              host: url.hostname,
              port: parseInt(url.port) || 6379,
              password: redisConfig.password || url.password,
              db: parseInt(url.pathname.slice(1)) || 0, // Extract DB from URL path
              retryDelayOnFailover: 100,
              maxRetriesPerRequest: 3,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [
    ChatController,
    StreamController,
    ConversationThreadController,
  ],
  providers: [
    ChatService,
    AgentConfigService,
    ConversationThreadService,
    ChatDatabaseService,
    UserAgentRunsQueries,
    AgentConfigQueries,
    UserMessagesQueries,
    ImageService
  ],
  exports: [
    ChatService,
    AgentConfigService,
    ConversationThreadService,
    ChatDatabaseService,
    UserAgentRunsQueries,
    AgentConfigQueries,
    UserMessagesQueries,
  ],
})
export class ChatModule {}
