import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { ClassificationService } from '../../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { S3Service } from '@shared/services/s3.service';
import { ComboProductCreateDto } from '../../../dto/request/create/combo-product-create.dto';
import { CreateClassificationDto, ClassificationResponseDto } from '../../../dto/classification.dto';

/**
 * Processor chuyên xử lý logic tạo sản phẩm combo
 * <PERSON>óc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class ComboProductProcessor {
  private readonly logger = new Logger(ComboProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo sản phẩm combo hoàn chỉnh
   */
  async createComboProduct(
    dto: ComboProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating COMBO product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm combo
    await this.validateComboProductData(dto);

    // BƯỚC 2: Xử lý custom fields
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // Combo products luôn có giá cố định
    product.typePrice = dto.typePrice;
    product.price = dto.price;

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (combo items)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto, []);

    // BƯỚC 8: Xử lý hình ảnh cho combo items
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto, dto.productType, Date.now());

    // BƯỚC 9: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);

      // Cập nhật advanced info với image keys nếu có hình ảnh combo items
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateAdvancedInfoWithImageKeys(advancedInfo.id, dto.productType, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 10: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 11: Lấy sản phẩm cuối cùng
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và combo items
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh combo items nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm combo
   */
  private async validateComboProductData(dto: ComboProductCreateDto): Promise<void> {
    // Kiểm tra combo items có tồn tại không (bắt buộc cho sản phẩm combo)
    if (!dto.comboItems || dto.comboItems.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Combo items are required for combo products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm combo)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for combo products',
      );
    }

    // Validate từng combo item
    for (const item of dto.comboItems) {
      if (!item.productId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          'Product ID is required for each combo item',
        );
      }
      if (!item.total || item.total <= 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          'Total quantity must be greater than 0 for each combo item',
        );
      }
    }

    this.logger.log(`Validated combo product data for: ${dto.name}`);
  }

  /**
   * Tạo advanced info cho sản phẩm combo
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    dto: ComboProductCreateDto,
    additionalData: any[],
  ): Promise<any> {
    this.logger.log(`Creating advanced info for combo product: ${productId}`);

    // Tạo advanced info cho sản phẩm combo với combo items
    return {
      id: Date.now(), // Temporary ID
      productId,
      productType,
      purchaseCount: dto.purchaseCount,
      comboItems: dto.comboItems,
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh combo items
   */
  private async createAdvancedImagesUploadUrls(
    dto: ComboProductCreateDto,
    productType: string,
    timestamp: number,
  ): Promise<any[]> {
    const uploadUrls: any[] = [];

    // Combo products thường không có hình ảnh riêng cho từng item
    // Hình ảnh chính của combo đã được xử lý ở bước trước
    // Có thể implement logic tạo presigned URLs cho combo-specific images nếu cần

    this.logger.log(`Created ${uploadUrls.length} upload URLs for combo images`);
    return uploadUrls;
  }

  /**
   * Cập nhật advanced info với image keys
   */
  private async updateAdvancedInfoWithImageKeys(
    advancedInfoId: number,
    productType: string,
    advancedImagesUploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement logic từ service gốc
    // Cập nhật advanced info với image keys nếu có hình ảnh combo items
    
    this.logger.log(`Updating advanced info with image keys for ${productType} product: ${advancedInfoId}`);
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: CreateClassificationDto[],
    userId: number,
  ): Promise<ClassificationResponseDto[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for combo product: ${productId}`);

    const createdClassifications: ClassificationResponseDto[] = [];

    // Tạo từng classification
    for (const classificationDto of classificationsDto) {
      try {
        // Tạo classification thông qua ClassificationService
        const createdClassification = await this.classificationService.create(
          productId,
          classificationDto,
          userId,
        );

        createdClassifications.push(createdClassification);
        this.logger.log(`Created classification: ${createdClassification.id} for combo product: ${productId}`);
      } catch (error) {
        this.logger.error(`Failed to create classification for combo product ${productId}: ${error.message}`, error.stack);
        throw error;
      }
    }

    this.logger.log(`Successfully created ${createdClassifications.length} classifications for combo product: ${productId}`);
    return createdClassifications;
  }
}
