### Environment Variables
### Thiết lập các biến môi trường cho testing

# Development Environment
@baseUrl = http://localhost:3000/api/v1
@token = your_jwt_token_here

# Staging Environment
# @baseUrl = https://staging-api.redai.com/api/v1
# @token = your_staging_jwt_token_here

# Production Environment
# @baseUrl = https://api.redai.com/api/v1
# @token = your_production_jwt_token_here

### Test Authentication
### Kiểm tra token có hoạt động không

GET {{baseUrl}}/user/products?page=1&limit=1
Authorization: Bearer {{token}}

### Test API Health
### Kiểm tra API có hoạt động không

GET {{baseUrl}}/health

### Login to get token (if needed)
### Đăng nhập để lấy token

POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}

### Refresh token (if needed)
### Làm mới token

POST {{baseUrl}}/auth/refresh
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "refreshToken": "your_refresh_token_here"
}

### Test User Profile
### Kiểm tra thông tin user

GET {{baseUrl}}/user/profile
Authorization: Bearer {{token}}

### Instructions for Testing
### Hướng dẫn sử dụng

# 1. Cập nhật baseUrl và token ở trên
# 2. Chạy test authentication trước
# 3. Nếu token hết hạn, sử dụng login hoặc refresh
# 4. Chạy các test theo thứ tự:
#    - 01-physical-product.http
#    - 02-digital-product.http  
#    - 03-event-product.http
#    - 04-service-product.http
#    - 05-combo-product.http
#    - 06-batch-create-products.http
#    - 07-product-management.http
#    - 08-error-cases.http

### Common Response Codes
### Các mã phản hồi thường gặp

# 200 OK - Thành công
# 201 Created - Tạo thành công
# 400 Bad Request - Dữ liệu không hợp lệ
# 401 Unauthorized - Chưa đăng nhập hoặc token không hợp lệ
# 403 Forbidden - Không có quyền truy cập
# 404 Not Found - Không tìm thấy resource
# 422 Unprocessable Entity - Validation error
# 500 Internal Server Error - Lỗi server

### Sample Success Response
### Mẫu response thành công

# {
#   "success": true,
#   "message": "Tạo sản phẩm thành công",
#   "data": {
#     "id": 1,
#     "name": "Áo thun nam",
#     "productType": "PHYSICAL",
#     "price": {
#       "listPrice": 300000,
#       "salePrice": 250000,
#       "currency": "VND"
#     },
#     "uploadUrls": {
#       "imagesUploadUrls": ["https://s3.amazonaws.com/..."]
#     }
#   }
# }

### Sample Error Response
### Mẫu response lỗi

# {
#   "success": false,
#   "message": "Validation failed",
#   "errors": [
#     {
#       "field": "name",
#       "message": "Tên sản phẩm không được để trống"
#     }
#   ]
# }
