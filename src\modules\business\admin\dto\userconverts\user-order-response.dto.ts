import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ProductInfo, BillInfo, LogisticInfo } from '../interfaces';

/**
 * DTO cho response trả về thông tin đơn hàng của người dùng
 */
export class UserOrderResponseDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
    examples: [1, 2, 3]
  })
  @IsNotEmpty({ message: 'ID đơn hàng không được để trống' })
  @IsNumber({}, { message: 'ID đơn hàng phải là số' })
  id: number;

  @ApiProperty({
    description: 'ID của khách hàng đặt đơn',
    example: 1,
    examples: [1, 2, 3, null],
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID khách hàng phải là số' })
  userConvertCustomerId: number | null;

  @ApiProperty({
    description: 'ID của người dùng sở hữu đơn hàng',
    example: 1,
    examples: [1, 2, 3, null],
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  userId: number | null;

  @ApiProperty({
    description: 'Thông tin sản phẩm (JSON)',
    example: [
      {
        productId: 1,
        name: 'Sản phẩm A',
        quantity: 2,
        price: 100000,
      },
    ],
    examples: [
      [
        {
          productId: 1,
          name: 'Sản phẩm A',
          quantity: 2,
          price: 100000,
        },
      ],
      [
        {
          productId: 1,
          name: 'Sản phẩm A',
          quantity: 2,
          price: 100000,
        },
        {
          productId: 2,
          name: 'Sản phẩm B',
          quantity: 1,
          price: 150000,
        },
      ],
      null
    ],
    nullable: true,
    type: () => [ProductInfo]
  })
  @IsOptional()
  @IsArray({ message: 'Thông tin sản phẩm phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => ProductInfo)
  productInfo: ProductInfo[] | null;

  @ApiProperty({
    description: 'Thông tin hóa đơn (JSON)',
    example: {
      subtotal: 200000,
      tax: 20000,
      shipping: 30000,
      total: 250000,
      paymentMethod: 'COD',
    },
    examples: [
      {
        subtotal: 200000,
        tax: 20000,
        shipping: 30000,
        total: 250000,
        paymentMethod: 'COD',
      },
      {
        subtotal: 150000,
        total: 150000,
        paymentMethod: 'Banking',
      },
      null
    ],
    nullable: true,
    type: BillInfo
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin hóa đơn phải là đối tượng JSON' })
  @Type(() => BillInfo)
  billInfo: BillInfo | null;

  @ApiProperty({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true,
    examples: [true, false]
  })
  @IsNotEmpty({ message: 'Trường hasShipping không được để trống' })
  @IsBoolean({ message: 'Trường hasShipping phải là giá trị boolean' })
  hasShipping: boolean;

  @ApiProperty({
    description: 'Trạng thái vận chuyển (ví dụ: pending, shipped, delivered)',
    example: 'pending',
    examples: ['pending', 'shipped', 'delivered', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Trạng thái vận chuyển phải là chuỗi' })
  shippingStatus: string | null;

  @ApiProperty({
    description: 'Thông tin vận chuyển chi tiết (JSON)',
    example: {
      address: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
      carrier: 'GHN',
      trackingNumber: 'GHN123456789',
    },
    examples: [
      {
        address: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
        carrier: 'GHN',
        trackingNumber: 'GHN123456789',
      },
      {
        address: 'Số 2, Đường DEF, Quận UVW, Hà Nội',
        recipient: {
          name: 'Nguyễn Văn A',
          phone: '0912345678'
        }
      },
      null
    ],
    nullable: true,
    type: LogisticInfo
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin vận chuyển phải là đối tượng JSON' })
  @Type(() => LogisticInfo)
  logisticInfo: LogisticInfo | null;

  @ApiProperty({
    description: 'Thời gian tạo đơn hàng (millis)',
    example: 1625097600000,
    examples: [1625097600000, 1630000000000]
  })
  @IsNotEmpty({ message: 'Thời gian tạo không được để trống' })
  @IsNumber({}, { message: 'Thời gian tạo phải là số' })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật đơn hàng (millis)',
    example: 1625184000000,
    examples: [1625184000000, 1640000000000]
  })
  @IsNotEmpty({ message: 'Thời gian cập nhật không được để trống' })
  @IsNumber({}, { message: 'Thời gian cập nhật phải là số' })
  updatedAt: number;

  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    examples: ['website', 'app', 'phone', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Nguồn đơn hàng phải là chuỗi' })
  source: string | null;
}
