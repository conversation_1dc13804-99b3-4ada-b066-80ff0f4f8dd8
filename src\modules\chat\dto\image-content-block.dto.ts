import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

/**
 * Image content block DTO for Facebook Messenger-style messaging
 * Simple version with only essential fields: type and fileId
 */
export class ImageContentBlockDto {
  @ApiProperty({
    description: "The type of content block, fixed to 'image'.",
    enum: ['image'],
    example: 'image',
  })
  @IsEnum(['image'])
  @IsNotEmpty()
  type: 'image';

  @ApiProperty({
    description: 'The unique ID of the uploaded image - must be a valid UUID.',
    example: 'a9b1c1d8-e3f4-5a6b-7c8d-9e0f1a2b3c4d',
  })
  @IsUUID(4, { message: 'fileId must be a valid UUID' })
  @IsNotEmpty()
  fileId: string;

  @ApiProperty({
    description: 'The display name of the image.',
    example: 'Product Screenshot',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'path of th image',
    example: '/path/to/image.png',
  })
  @IsString()
  @IsNotEmpty()
  path: string;

  @ApiProperty({
    description: 'tags of the image',
    example: ['funny', 'cute'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[] = [];

  @ApiProperty({
    description: 'An optional description of the image.',
    example: 'This is a screenshot of the product detail page.',
    required: false,
  })
  @IsOptional()
  @IsString()
  desc?: string;
}
