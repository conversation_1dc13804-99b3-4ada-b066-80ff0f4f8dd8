import { Injectable, Logger } from '@nestjs/common';
import { UserOrderRepository, UserProductRepository, UserConvertCustomerRepository, UserAddressRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto, UserOrderStatusResponseDto, OrderStatusStatsDto, ShippingStatusStatsDto, UserConvertCustomerListItemDto, CreateUserOrderDto, UpdateUserOrderDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { OrderStatusEnum, ShippingStatusEnum, PaymentStatusEnum, PaymentMethodEnum } from '../../enums';
import { AddressValidationService } from './address-validation.service';
import { ShopShippingService } from './shop-shipping.service';
import { Transactional } from 'typeorm-transactional';
import { UserOrder } from '../../entities';
import { GHTKShipmentService } from './ghtk-shipment.service';
import { GHNShipmentService } from './ghn-shipment.service';
import { UserShopInfoService } from './user-shop-info.service';
import { UserProviderShipmentService } from '@modules/integration/services/user-provider-shipment.service';
import { ProviderShipmentType } from '@modules/integration/constants/provider-shipment-type.enum';
import { OrderStatusConfigHelper } from '../helpers/order-status-config.helper';
import { GHTKIntegrationHelper } from '../helpers/ghtk-integration.helper';
import { BulkDeleteUserOrderResponseDto, BulkDeleteUserOrderResultItemDto } from '../dto/bulk-delete-user-order-response.dto';
import { BulkDeleteUserOrderDto } from '../dto/bulk-delete-user-order.dto';

/**
 * Service xử lý logic nghiệp vụ cho đơn hàng của người dùng
 */
@Injectable()
export class UserOrderService {
  private readonly logger = new Logger(UserOrderService.name);

  constructor(
    private readonly userOrderRepository: UserOrderRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly userAddressRepository: UserAddressRepository,
    private readonly ghtkShipmentService: GHTKShipmentService,
    private readonly ghnShipmentService: GHNShipmentService,
    private readonly userShopInfoService: UserShopInfoService,
    private readonly userProviderShipmentService: UserProviderShipmentService,
    private readonly addressValidationService: AddressValidationService,
    private readonly shopShippingService: ShopShippingService,
    private readonly orderStatusConfigHelper: OrderStatusConfigHelper,
    private readonly ghtkIntegrationHelper: GHTKIntegrationHelper,
  ) {}

  /**
   * Tạo đơn hàng mới
   * @param userId ID người dùng
   * @param createOrderDto Dữ liệu tạo đơn hàng
   * @returns Thông tin đơn hàng đã tạo
   */
  @Transactional()
  async createOrder(userId: number, createOrderDto: CreateUserOrderDto): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Tạo đơn hàng mới cho userId=${userId}`);

      // 1. Validate và lấy thông tin sản phẩm
      const productInfoData = await this.validateAndGetProductInfos(userId, createOrderDto.products);

      // 2. Validate thông tin khách hàng
      const customerId = createOrderDto.customerInfo.customerId;
      const existingCustomer = await this.userConvertCustomerRepository.findById(customerId);

      if (!existingCustomer || existingCustomer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND,
          'Khách hàng không tồn tại hoặc không thuộc về bạn'
        );
      }

      // 3. Xử lý thông tin logistics và tính phí vận chuyển
      const processedLogisticInfo = await this.processLogisticInfo(userId, createOrderDto.logisticInfo, existingCustomer);

      // 4. Validate shopId và lấy thông tin shop
      const shopInfo = await this.userShopInfoService.getShopById(createOrderDto.shopId, userId);
      if (!shopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Shop với ID ${createOrderDto.shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // 4.1. Validate shop có đầy đủ thông tin cho shipping không (nếu có yêu cầu vận chuyển)
      const hasShipping = createOrderDto.hasShipping !== false;
      if (hasShipping) {
        await this.shopShippingService.validateShopForShipping(createOrderDto.shopId, userId);
      }

      // 5. Xử lý thông tin vận chuyển và phí vận chuyển
      let shippingFee = 0;
      let selectedCarrier: string | null = null;
      let shippingServiceType: string | null = null;

      if (hasShipping && processedLogisticInfo) {
        // Sử dụng thông tin vận chuyển đã được tính toán trước (khuyến nghị)
        if (createOrderDto.billInfo.selectedCarrier && createOrderDto.billInfo.shippingFee !== undefined) {
          selectedCarrier = createOrderDto.billInfo.selectedCarrier;
          shippingFee = createOrderDto.billInfo.shippingFee;
          shippingServiceType = createOrderDto.billInfo.shippingServiceType || 'standard';

          this.logger.log(`Sử dụng thông tin vận chuyển đã tính toán: ${selectedCarrier}, phí: ${shippingFee} VND`);
        } else {
          // Fallback: Tính phí vận chuyển tự động (không khuyến nghị)
          this.logger.warn('Không có thông tin vận chuyển đã tính toán, đang tính toán tự động...');

          const shippingResult = await this.calculateAndSelectShipping(
            userId,
            createOrderDto.shopId,
            productInfoData,
            processedLogisticInfo,
            createOrderDto.logisticInfo?.carrier
            // ❌ Xóa receiverPaysShipping vì giờ lấy từ User Provider Shipment setting
          );

          shippingFee = shippingResult.fee;
          selectedCarrier = shippingResult.carrier;
          shippingServiceType = shippingResult.serviceType;

          this.logger.log(`Đã tính phí vận chuyển tự động: ${shippingFee} VND với ${selectedCarrier}`);
        }

        // Cập nhật logistic info với thông tin vận chuyển
        processedLogisticInfo.carrier = selectedCarrier;
        processedLogisticInfo.shippingFee = shippingFee;
        processedLogisticInfo.serviceType = shippingServiceType;
      }

      // 6. Cập nhật bill info với phí vận chuyển và tính toán tự động
      const calculatedTotal = (createOrderDto.billInfo.subtotal || 0) +
                             shippingFee +
                             (createOrderDto.billInfo.tax || 0) -
                             (createOrderDto.billInfo.discount || 0);

      const updatedBillInfo = {
        ...createOrderDto.billInfo,
        shippingFee,
        selectedCarrier,
        shippingServiceType,
        total: createOrderDto.billInfo.total || calculatedTotal, // Sử dụng total từ user hoặc tự động tính
        paymentStatus: PaymentStatusEnum.PENDING, // Luôn bắt đầu với PENDING, không cho phép client set
      };

      // 6.1. Validate payment logic
      await this.validatePayment(updatedBillInfo, userId);

      // 7. Tạo đơn hàng với các giá trị mặc định
      const orderData: Partial<UserOrder> = {
        userId,
        userConvertCustomerId: customerId,
        productInfo: productInfoData,
        billInfo: updatedBillInfo,
        hasShipping,
        shippingStatus: createOrderDto.shippingStatus || ShippingStatusEnum.PENDING,
        logisticInfo: processedLogisticInfo,
        orderStatus: createOrderDto.orderStatus || OrderStatusEnum.PENDING,
        source: createOrderDto.source || 'website',
      };

      // Thêm thông tin bổ sung nếu có
      if (createOrderDto.note || createOrderDto.tags) {
        orderData.logisticInfo = {
          ...orderData.logisticInfo,
          note: createOrderDto.note,
          tags: createOrderDto.tags,
        };
      }

      const createdOrder = await this.userOrderRepository.createOrder(orderData);

      this.logger.log(`Đã tạo đơn hàng thành công với ID=${createdOrder.id}`);

      // 8. Submit đơn hàng đến đơn vị vận chuyển nếu có yêu cầu vận chuyển
      let shippingSubmitSuccess = true;
      let shippingErrorMessage = '';

      if (hasShipping && selectedCarrier && processedLogisticInfo) {
        try {
          // Validate cấu hình provider trước khi submit (bắt buộc có config để tạo vận đơn thực tế)
          await this.validateUserProviderConfig(userId, selectedCarrier);

          await this.submitOrderToShippingProvider(createdOrder, selectedCarrier, processedLogisticInfo, productInfoData);
          this.logger.log(`Đã submit đơn hàng ${createdOrder.id} đến ${selectedCarrier} thành công`);
        } catch (shippingError) {
          shippingSubmitSuccess = false;
          shippingErrorMessage = shippingError.message;

          // Log chi tiết lỗi để debug
          this.logger.error(`Lỗi khi submit đơn hàng ${createdOrder.id} đến ${selectedCarrier}:`, {
            error: shippingError.message,
            stack: shippingError.stack,
            response: shippingError.response?.data || shippingError.response,
            status: shippingError.response?.status,
            config: shippingError.config ? {
              url: shippingError.config.url,
              method: shippingError.config.method,
              data: shippingError.config.data
            } : null,
            orderId: createdOrder.id,
            userId,
            selectedCarrier,
            processedLogisticInfo
          });

          // Thay vì xóa đơn hàng, cập nhật trạng thái để xử lý thủ công
          const updatedLogisticInfo = {
            ...processedLogisticInfo,
            shippingError: shippingError.message,
            shippingErrorDetails: {
              message: shippingError.message,
              response: shippingError.response?.data || shippingError.response,
              status: shippingError.response?.status,
              timestamp: Date.now()
            },
            needsManualProcessing: true,
            failedAt: Date.now(),
            selectedCarrier,
            shippingFee
          };

          await this.userOrderRepository.updateOrder(createdOrder.id, userId, {
            shippingStatus: ShippingStatusEnum.DELIVERY_FAILED, // Set DELIVERY_FAILED khi gửi vận chuyển lỗi
            logisticInfo: updatedLogisticInfo
          });

          this.logger.log(`Đã cập nhật đơn hàng ${createdOrder.id} với trạng thái delivery_failed để xử lý thủ công`);
        }
      }

      // 9. Trả về thông tin đơn hàng đã tạo với thông báo phù hợp
      const orderResponse = await this.findById(createdOrder.id, userId);

      // Thêm thông báo cho user về trạng thái shipping
      if (!shippingSubmitSuccess && hasShipping) {
        (orderResponse as any).message = 'Đơn hàng đã được tạo thành công. Tuy nhiên, có lỗi khi tạo vận đơn. Chúng tôi sẽ xử lý thủ công và liên hệ với bạn sớm nhất.';
        (orderResponse as any).requiresManualProcessing = true;
        (orderResponse as any).shippingError = shippingErrorMessage;
      } else if (hasShipping) {
        (orderResponse as any).message = 'Đơn hàng và vận đơn đã được tạo thành công.';
      } else {
        (orderResponse as any).message = 'Đơn hàng đã được tạo thành công.';
      }

      return orderResponse;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi tạo đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Validate và lấy thông tin chi tiết sản phẩm
   * @param userId ID người dùng
   * @param products Danh sách sản phẩm trong đơn hàng
   * @returns Thông tin chi tiết sản phẩm
   */
  private async validateAndGetProductInfos(userId: number, products: any[]): Promise<Record<string, unknown>> {
    const productInfos: any[] = [];

    for (const productItem of products) {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user không
      const product = await this.userProductRepository.findById(productItem.productId);
      if (!product || product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Sản phẩm với ID ${productItem.productId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // Tính toán giá và tổng tiền
      let unitPrice = 0;
      if (product.price && typeof product.price === 'object') {
        unitPrice = (product.price as any).salePrice || (product.price as any).listPrice || 0;
      }

      const totalPrice = unitPrice * productItem.quantity;

      // Lấy thông tin vận chuyển từ shipmentConfig
      const shipmentConfig = product.shipmentConfig || {};
      const weight = shipmentConfig.weightGram || 200; // Mặc định 200g nếu không có
      const dimensions = {
        length: shipmentConfig.lengthCm || 30,
        width: shipmentConfig.widthCm || 25,
        height: shipmentConfig.heightCm || 5
      };

      productInfos.push({
        productId: product.id,
        name: product.name,
        quantity: productItem.quantity,
        unitPrice,
        totalPrice,
        description: product.description,
        images: product.images,
        weight, // Trọng lượng từ shipmentConfig
        dimensions, // Kích thước từ shipmentConfig
        shipmentConfig: product.shipmentConfig, // Toàn bộ config để sử dụng sau này
      });
    }

    return { products: productInfos };
  }

  /**
   * Xử lý thông tin logistics và địa chỉ giao hàng
   * @param userId ID người dùng
   * @param logisticInfo Thông tin logistics từ request
   * @param customer Thông tin customer để lấy địa chỉ mặc định
   * @returns Thông tin logistics đã xử lý
   */
  private async processLogisticInfo(userId: number, logisticInfo: any, customer: any): Promise<any> {
    let deliveryAddressInfo = customer.address || ''; // Mặc định sử dụng địa chỉ customer
    let recipientName = customer.name || '';
    let recipientPhone = customer.phone || '';

    if (logisticInfo?.deliveryAddress) {
      // Nếu có thông tin địa chỉ trong logistic info
      if (logisticInfo.deliveryAddress.addressId) {
        // Trường hợp chọn địa chỉ có sẵn
        const existingAddress = await this.userAddressRepository.findByIdAndUserId(
          logisticInfo.deliveryAddress.addressId,
          userId
        );

        if (!existingAddress) {
          throw new AppException(
            BUSINESS_ERROR_CODES.ADDRESS_NOT_FOUND,
            'Địa chỉ không tồn tại hoặc không thuộc về bạn'
          );
        }

        this.logger.log(`Found existing address for addressId ${logisticInfo.deliveryAddress.addressId}:`, {
          id: existingAddress.id,
          address: existingAddress.address,
          ward: existingAddress.ward,
          district: existingAddress.district,
          province: existingAddress.province,
          hamlet: existingAddress.hamlet,
          recipientName: existingAddress.recipientName,
          recipientPhone: existingAddress.recipientPhone
        });

        deliveryAddressInfo = this.formatAddressString(existingAddress);
        recipientName = existingAddress.recipientName;
        recipientPhone = existingAddress.recipientPhone;

        // Lưu thông tin địa chỉ chi tiết cho GHTK
        logisticInfo.addressDetails = {
          fullAddress: deliveryAddressInfo,
          street: existingAddress.address || '', // Địa chỉ số nhà, đường
          ward: existingAddress.ward || '',
          district: existingAddress.district || '',
          province: existingAddress.province || '',
          hamlet: existingAddress.hamlet || ''
        };

        this.logger.log(`Address details for GHTK:`, logisticInfo.addressDetails);
      } else if (logisticInfo.deliveryAddress.newAddress) {
        // Trường hợp tạo địa chỉ mới
        const newAddressData = {
          ...logisticInfo.deliveryAddress.newAddress,
          userId,
          isDefault: false, // Địa chỉ từ order không set làm mặc định
        };

        const createdAddress = await this.userAddressRepository.createAddress(newAddressData);
        deliveryAddressInfo = this.formatAddressString(createdAddress);
        recipientName = createdAddress.recipientName;
        recipientPhone = createdAddress.recipientPhone;

        // Lưu thông tin địa chỉ chi tiết cho GHTK (tương tự như existingAddress)
        logisticInfo.addressDetails = {
          fullAddress: deliveryAddressInfo,
          street: createdAddress.address || '', // Địa chỉ số nhà, đường
          ward: createdAddress.ward || '',
          district: createdAddress.district || '',
          province: createdAddress.province || '',
          hamlet: createdAddress.hamlet || ''
        };

        this.logger.log(`Address details for GHTK (new address):`, logisticInfo.addressDetails);
      } else {
        // Fallback: Không có addressId hoặc newAddress, sử dụng địa chỉ mặc định của customer
        this.logger.warn('No specific address provided, using customer default address');

        // Tìm địa chỉ mặc định của customer
        const defaultAddress = await this.userAddressRepository.findDefaultByUserId(userId);

        if (defaultAddress) {
          this.logger.log(`Using customer default address:`, {
            id: defaultAddress.id,
            address: defaultAddress.address,
            ward: defaultAddress.ward,
            district: defaultAddress.district,
            province: defaultAddress.province
          });

          deliveryAddressInfo = this.formatAddressString(defaultAddress);
          recipientName = defaultAddress.recipientName;
          recipientPhone = defaultAddress.recipientPhone;

          // Lưu thông tin địa chỉ chi tiết cho GHTK
          logisticInfo.addressDetails = {
            fullAddress: deliveryAddressInfo,
            street: defaultAddress.address || '',
            ward: defaultAddress.ward || '',
            district: defaultAddress.district || '',
            province: defaultAddress.province || '',
            hamlet: defaultAddress.hamlet || ''
          };

          this.logger.log(`Address details for GHTK (default address):`, logisticInfo.addressDetails);
        } else {
          this.logger.warn('No default address found, using customer address from customer info');
          // Fallback cuối cùng: sử dụng địa chỉ từ customer info
          deliveryAddressInfo = customer.address || '';
        }
      }
    }

    return {
      shippingMethod: logisticInfo?.shippingMethod,
      carrier: logisticInfo?.carrier,
      shippingNote: logisticInfo?.shippingNote,
      deliveryAddress: deliveryAddressInfo,
      recipientName,
      recipientPhone,
    };
  }

  /**
   * Format địa chỉ thành chuỗi
   * @param address Thông tin địa chỉ
   * @returns Chuỗi địa chỉ đầy đủ
   */
  private formatAddressString(address: any): string {
    const parts = [
      address.address,
      address.ward,
      address.district,
      address.province,
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Lấy danh sách đơn hàng của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserOrderDto): Promise<PaginatedResult<UserOrderListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${userId}`);

      // Lấy danh sách đơn hàng từ repository
      const result = await this.userOrderRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO response với thông tin customer đầy đủ
      const items = result.items.map(order => {
        const orderDto = plainToInstance(UserOrderListItemDto, order, { excludeExtraneousValues: true });

        // Map thông tin customer nếu có
        if (order.userConvertCustomer) {
          orderDto.userConvertCustomer = plainToInstance(
            UserConvertCustomerListItemDto,
            order.userConvertCustomer,
            { excludeExtraneousValues: true }
          );
        }

        return orderDto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID đơn hàng
   * @param userId ID người dùng
   * @returns Chi tiết đơn hàng
   */
  async findById(id: number, userId: number): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${userId}`);

      // Lấy đơn hàng từ repository
      const order = await this.userOrderRepository.findById(id);

      // Kiểm tra đơn hàng tồn tại
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${id}`
        );
      }

      // Kiểm tra đơn hàng thuộc về người dùng
      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          `Bạn không có quyền truy cập đơn hàng này`
        );
      }

      // Chuyển đổi sang DTO response với xử lý đặc biệt cho JSON fields
      const orderDto = plainToInstance(UserOrderResponseDto, order, { excludeExtraneousValues: true });

      // Xử lý productInfo - từ database có cấu trúc {products: [...]}
      if (order.productInfo && typeof order.productInfo === 'object') {
        if ((order.productInfo as any).products && Array.isArray((order.productInfo as any).products)) {
          orderDto.productInfo = (order.productInfo as any).products;
        } else {
          orderDto.productInfo = order.productInfo as any;
        }
      }

      // Xử lý billInfo - giữ nguyên object
      if (order.billInfo && typeof order.billInfo === 'object') {
        orderDto.billInfo = order.billInfo as any;
      }

      // Xử lý logisticInfo - giữ nguyên object
      if (order.logisticInfo && typeof order.logisticInfo === 'object') {
        orderDto.logisticInfo = order.logisticInfo as any;
      }

      // Thêm cấu hình trạng thái đơn hàng và vận chuyển
      orderDto.orderStatusConfig = this.orderStatusConfigHelper.getOrderStatusConfig(order.orderStatus);
      orderDto.shippingStatusConfig = this.orderStatusConfigHelper.getShippingStatusConfig(order.shippingStatus);

      return orderDto;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật đơn hàng
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param updateOrderDto Dữ liệu cập nhật
   * @returns Đơn hàng đã cập nhật
   */
  @Transactional()
  async updateOrder(orderId: number, userId: number, updateOrderDto: UpdateUserOrderDto): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Cập nhật đơn hàng id=${orderId} cho userId=${userId}`);

      // Kiểm tra đơn hàng có tồn tại và thuộc về user không
      const existingOrder = await this.userOrderRepository.findById(orderId);
      if (!existingOrder) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${orderId}`
        );
      }

      if (existingOrder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          `Bạn không có quyền cập nhật đơn hàng này`
        );
      }

      // Kiểm tra trạng thái đơn hàng có thể cập nhật không
      if (existingOrder.orderStatus === OrderStatusEnum.COMPLETED ||
          existingOrder.orderStatus === OrderStatusEnum.CANCELLED) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          `Không thể cập nhật đơn hàng đã hoàn thành hoặc đã hủy`
        );
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<UserOrder> = {};

      if (updateOrderDto.billInfo) {
        updateData.billInfo = updateOrderDto.billInfo as any;
      }

      if (updateOrderDto.logisticInfo) {
        updateData.logisticInfo = updateOrderDto.logisticInfo as any;
      }

      if (updateOrderDto.orderStatus) {
        updateData.orderStatus = updateOrderDto.orderStatus;
      }

      if (updateOrderDto.shippingStatus) {
        updateData.shippingStatus = updateOrderDto.shippingStatus;
      }

      if (updateOrderDto.hasShipping !== undefined) {
        updateData.hasShipping = updateOrderDto.hasShipping;
      }

      if (updateOrderDto.source) {
        updateData.source = updateOrderDto.source;
      }

      // Thực hiện cập nhật
      const updatedOrder = await this.userOrderRepository.updateOrder(orderId, userId, updateData);

      // Chuyển đổi sang DTO response
      return plainToInstance(UserOrderResponseDto, updatedOrder, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi cập nhật đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Validate cấu hình provider của user
   * @param userId ID người dùng
   * @param carrier Đơn vị vận chuyển
   * @returns Promise<void>
   */
  private async validateUserProviderConfig(userId: number, carrier: string): Promise<void> {
    this.logger.log(`Validating provider config for user ${userId}, carrier: ${carrier}`);

    let providerType: ProviderShipmentType;

    if (carrier === 'GHN') {
      providerType = ProviderShipmentType.GHN;
    } else if (carrier === 'GHTK') {
      providerType = ProviderShipmentType.GHTK;
    } else {
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Đơn vị vận chuyển không được hỗ trợ: ${carrier}`
      );
    }

    // Kiểm tra user đã cấu hình provider chưa
    const config = await this.userProviderShipmentService.getDecryptedConfig(userId, providerType);

    this.logger.log(`Validation config retrieved for user ${userId}, carrier ${carrier}:`, {
      hasConfig: !!config,
      configKeys: config ? Object.keys(config) : [],
      hasToken: config ? !!config.token : false,
      hasShopId: config ? !!config.shopId : false
    });

    if (!config) {
      this.logger.error(`No config found during validation for user ${userId}, carrier ${carrier}`);
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Bạn chưa cấu hình đơn vị vận chuyển ${carrier}. Vui lòng cấu hình trong phần quản lý nhà cung cấp vận chuyển trước khi sử dụng.`
      );
    }

    // Validate token và thông tin cần thiết
    if (carrier === 'GHN' && (!config.token || !config.shopId)) {
      this.logger.error(`Incomplete GHN config during validation for user ${userId}:`, {
        hasToken: !!config.token,
        hasShopId: !!config.shopId,
        tokenLength: config.token ? config.token.length : 0,
        shopId: config.shopId ? 'present' : 'missing'
      });
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Cấu hình ${carrier} không đầy đủ. Vui lòng kiểm tra lại token và shop ID.`
      );
    }

    if (carrier === 'GHTK' && !config.token) {
      this.logger.error(`Incomplete GHTK config during validation for user ${userId}:`, {
        hasToken: !!config.token
      });
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Cấu hình ${carrier} không đầy đủ. Vui lòng kiểm tra lại token.`
      );
    }

    this.logger.log(`Provider config validation passed for user ${userId}, carrier ${carrier}`);
  }

  /**
   * Kiểm tra cấu hình provider của user (không throw error)
   * @param userId ID người dùng
   * @param carrier Đơn vị vận chuyển
   * @returns Promise<{isConfigured: boolean, config?: any, message?: string}>
   */
  private async checkUserProviderConfig(userId: number, carrier: string): Promise<{
    isConfigured: boolean;
    config?: any;
    message?: string;
  }> {
    try {
      this.logger.log(`Checking provider config for user ${userId}, carrier: ${carrier}`);

      let providerType: ProviderShipmentType;

      if (carrier === 'GHN') {
        providerType = ProviderShipmentType.GHN;
      } else if (carrier === 'GHTK') {
        providerType = ProviderShipmentType.GHTK;
      } else {
        return {
          isConfigured: false,
          message: `Đơn vị vận chuyển không được hỗ trợ: ${carrier}`
        };
      }

      // Kiểm tra user đã cấu hình provider chưa
      const config = await this.userProviderShipmentService.getDecryptedConfig(userId, providerType);

      this.logger.log(`Config retrieved for user ${userId}, carrier ${carrier}:`, {
        hasConfig: !!config,
        configKeys: config ? Object.keys(config) : [],
        hasToken: config ? !!config.token : false,
        hasShopId: config ? !!config.shopId : false
      });

      if (!config) {
        this.logger.warn(`No config found for user ${userId}, carrier ${carrier}`);
        return {
          isConfigured: false,
          message: `Bạn chưa cấu hình đơn vị vận chuyển ${carrier}. Vui lòng cấu hình trong phần quản lý nhà cung cấp vận chuyển để có thể tạo đơn hàng thực tế.`
        };
      }

      // Validate token và thông tin cần thiết
      if (carrier === 'GHN' && (!config.token || !config.shopId)) {
        this.logger.warn(`Incomplete GHN config for user ${userId}:`, {
          hasToken: !!config.token,
          hasShopId: !!config.shopId,
          tokenLength: config.token ? config.token.length : 0,
          shopId: config.shopId ? 'present' : 'missing'
        });
        return {
          isConfigured: false,
          config,
          message: `Cấu hình ${carrier} không đầy đủ. Vui lòng kiểm tra lại token và shop ID.`
        };
      }

      if (carrier === 'GHTK' && !config.token) {
        this.logger.warn(`Incomplete GHTK config for user ${userId}:`, {
          hasToken: !!config.token
        });
        return {
          isConfigured: false,
          config,
          message: `Cấu hình ${carrier} không đầy đủ. Vui lòng kiểm tra lại token.`
        };
      }

      this.logger.log(`Config validation passed for user ${userId}, carrier ${carrier}`);
      return {
        isConfigured: true,
        config
      };
    } catch (error) {
      this.logger.error(`Error checking provider config for user ${userId}, carrier ${carrier}:`, error);
      return {
        isConfigured: false,
        message: `Lỗi khi kiểm tra cấu hình ${carrier}: ${error.message}`
      };
    }
  }

  /**
   * Tính phí và chọn đơn vị vận chuyển
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param productInfo Thông tin sản phẩm
   * @param logisticInfo Thông tin logistics
   * @param preferredCarrier Đơn vị vận chuyển ưu tiên
   * @param shippingPaymentType
   * @returns Thông tin vận chuyển đã chọn
   */
  private async calculateAndSelectShipping(
    userId: number,
    shopId: number,
    productInfo: any,
    logisticInfo: any,
    preferredCarrier?: string,
    receiverPaysShippingOverride?: boolean
  ): Promise<{ carrier: string; fee: number; serviceType: string }> {
    try {
      this.logger.log('Tính phí và chọn đơn vị vận chuyển', { preferredCarrier });

      // Tính tổng trọng lượng và giá trị đơn hàng
      const totalWeight = this.calculateTotalWeight(productInfo);
      const totalValue = this.calculateTotalValue(productInfo);

      // Định tuyến đơn vị vận chuyển theo rule
      const selectedCarrier = this.selectCarrierByRule(preferredCarrier);
      this.logger.log(`Selected carrier: ${selectedCarrier}, Preferred carrier: ${preferredCarrier}`);

      // Kiểm tra cấu hình provider của user (không throw error ngay)
      const configCheck = await this.checkUserProviderConfig(userId, selectedCarrier);

      // Lấy setting receiverPaysShipping từ User Provider Shipment
      // Nếu có override từ request thì dùng override, không thì dùng setting mặc định
      let receiverPaysShipping = receiverPaysShippingOverride;
      if (receiverPaysShipping === undefined && configCheck.isConfigured) {
        try {
          // Lấy từ User Provider Shipment setting
          const providerType = selectedCarrier === 'GHN' ? ProviderShipmentType.GHN : ProviderShipmentType.GHTK;
          const userProviderConfig = await this.userProviderShipmentService.findByTypeAndUserId(providerType, userId);
          if (userProviderConfig && userProviderConfig.length > 0) {
            receiverPaysShipping = userProviderConfig[0].receiverPaysShipping;
            this.logger.log(`Sử dụng receiverPaysShipping từ User Provider Shipment: ${receiverPaysShipping} (${receiverPaysShipping ? 'người nhận trả' : 'người gửi trả'})`);
          } else {
            receiverPaysShipping = false; // Mặc định false (người gửi trả)
            this.logger.log(`Không tìm thấy User Provider Shipment, sử dụng mặc định: false (người gửi trả)`);
          }
        } catch (error) {
          this.logger.warn(`Lỗi khi lấy setting receiverPaysShipping từ User Provider Shipment: ${error.message}`);
          receiverPaysShipping = false; // Mặc định false (người gửi trả)
        }
      } else if (receiverPaysShipping === undefined) {
        receiverPaysShipping = false; // Mặc định false (người gửi trả)
      }

      let shippingFee = 0;
      let serviceType = 'standard';

      // Nếu chưa có cấu hình, báo lỗi yêu cầu cấu hình
      if (!configCheck.isConfigured) {
        this.logger.error(`User ${userId} chưa cấu hình ${selectedCarrier}`);
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          configCheck.message || `Bạn chưa cấu hình đơn vị vận chuyển ${selectedCarrier}. Vui lòng cấu hình trong phần quản lý nhà cung cấp vận chuyển trước khi sử dụng.`
        );
      }

      // Có cấu hình, tính phí thực tế
      if (selectedCarrier === 'GHN') {
        try {
          const ghnFeeResult = await this.calculateGHNFee(userId, shopId, logisticInfo, totalWeight, totalValue, productInfo, receiverPaysShipping);
          shippingFee = ghnFeeResult.fee;
          serviceType = ghnFeeResult.serviceType;
        } catch (error) {
          this.logger.error(`Lỗi khi tính phí GHN: ${error.message}`, error);
          throw new AppException(
            BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
            `Không thể tính phí vận chuyển GHN: ${error.message}. Vui lòng kiểm tra lại cấu hình hoặc thử lại sau.`
          );
        }
      } else if (selectedCarrier === 'GHTK') {
        try {
          const ghtkFeeResult = await this.calculateGHTKFee(userId, shopId, logisticInfo, totalWeight, totalValue, receiverPaysShipping);
          shippingFee = ghtkFeeResult.fee;
          serviceType = ghtkFeeResult.serviceType;
        } catch (error) {
          this.logger.error(`Lỗi khi tính phí GHTK: ${error.message}`, error);
          throw new AppException(
            BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
            `Không thể tính phí vận chuyển GHTK: ${error.message}. Vui lòng kiểm tra lại cấu hình hoặc thử lại sau.`
          );
        }
      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Đơn vị vận chuyển không được hỗ trợ: ${selectedCarrier}`
        );
      }

      this.logger.log(`Đã chọn ${selectedCarrier} với phí ${shippingFee}đ`);

      return {
        carrier: selectedCarrier,
        fee: shippingFee,
        serviceType
      };
    } catch (error) {
      this.logger.error('Lỗi khi tính phí vận chuyển:', error);
      // Ném lỗi thay vì trả về phí mặc định để người dùng biết có vấn đề
      throw error;
    }
  }

  /**
   * Chọn đơn vị vận chuyển theo lựa chọn của người dùng
   * @param preferredCarrier Đơn vị vận chuyển người dùng chọn
   * @returns Đơn vị vận chuyển được chọn
   */
  private selectCarrierByRule(preferredCarrier?: string): string {
    // Người dùng phải chọn đơn vị vận chuyển, không tự động chọn
    if (preferredCarrier && ['GHN', 'GHTK'].includes(preferredCarrier)) {
      return preferredCarrier;
    }

    // Nếu người dùng không chọn, mặc định sử dụng GHN
    return 'GHN';
  }



  /**
   * Tính tổng trọng lượng sản phẩm
   * @param productInfo Thông tin sản phẩm
   * @returns Tổng trọng lượng (gram)
   */
  private calculateTotalWeight(productInfo: any): number {
    if (!productInfo?.products || !Array.isArray(productInfo.products)) {
      return 500; // Trọng lượng mặc định 500g
    }

    return productInfo.products.reduce((total: number, product: any) => {
      // Sử dụng trọng lượng thực từ shipmentConfig của sản phẩm
      const weight = product.weight || 200; // Trọng lượng mặc định 200g/sản phẩm (theo default của entity)
      const quantity = product.quantity || 1;
      return total + (weight * quantity);
    }, 0);
  }

  /**
   * Tính tổng trọng lượng sản phẩm cho GHTK (kg)
   * @param productInfo Thông tin sản phẩm
   * @returns Tổng trọng lượng (kg)
   */
  private calculateTotalWeightForGHTK(productInfo: any): number {
    const weightInGrams = this.calculateTotalWeight(productInfo);
    const weightInKg = weightInGrams / 1000; // Chuyển từ gram sang kg

    this.logger.log(`Weight conversion: ${weightInGrams}g → ${weightInKg}kg`);

    // GHTK yêu cầu tối thiểu 0.1kg
    return Math.max(weightInKg, 0.1);
  }

  /**
   * Tính tổng giá trị đơn hàng
   * @param productInfo Thông tin sản phẩm
   * @returns Tổng giá trị
   */
  private calculateTotalValue(productInfo: any): number {
    if (!productInfo?.products || !Array.isArray(productInfo.products)) {
      return 0;
    }

    return productInfo.products.reduce((total: number, product: any) => {
      const price = product.unitPrice || 0;
      const quantity = product.quantity || 1;
      return total + (price * quantity);
    }, 0);
  }

  /**
   * Tính phí vận chuyển GHN - Đã cải thiện với address validation
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param logisticInfo Thông tin logistics
   * @param weight Trọng lượng
   * @param value Giá trị đơn hàng
   * @param productInfo Thông tin sản phẩm để lấy kích thước
   * @param receiverPaysShipping Người nhận có trả phí vận chuyển không
   * @returns Phí vận chuyển GHN
   */
  private async calculateGHNFee(
    userId: number,
    shopId: number,
    logisticInfo: any,
    weight: number,
    value: number,
    productInfo?: any,
    receiverPaysShipping?: boolean
  ): Promise<{ fee: number; serviceType: string }> {
    try {
      // Validate và parse địa chỉ giao hàng bằng service mới
      const deliveryAddress = await this.addressValidationService.validateAndParseAddress(logisticInfo.deliveryAddress);

      if (!deliveryAddress.isValid) {
        this.logger.warn(`Delivery address parsing has low confidence: ${deliveryAddress.confidence}`, {
          address: logisticInfo.deliveryAddress,
          parsed: deliveryAddress
        });
      }

      // Lấy thông tin shop shipping từ service mới
      const shopShippingInfo = await this.shopShippingService.getCachedShopShippingInfo(shopId, userId);

      // Tính kích thước tổng hợp từ các sản phẩm (lấy kích thước lớn nhất)
      let maxLength = 30, maxWidth = 25, maxHeight = 5;
      if (productInfo?.products && Array.isArray(productInfo.products)) {
        productInfo.products.forEach((product: any) => {
          if (product.dimensions) {
            maxLength = Math.max(maxLength, product.dimensions.length || 30);
            maxWidth = Math.max(maxWidth, product.dimensions.width || 25);
            maxHeight = Math.max(maxHeight, product.dimensions.height || 5);
          }
        });
      }

      // Cập nhật config GHN service với config của user trước khi gọi API
      await this.updateGHNServiceConfig(userId);

      // Lấy user config để sử dụng shop ID đúng
      const userConfig = await this.userProviderShipmentService.getDecryptedConfig(userId, ProviderShipmentType.GHN);
      if (!userConfig || !userConfig.shopId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_INVALID_CONFIG,
          'Chưa cấu hình Shop ID cho GHN. Vui lòng cấu hình trong phần quản lý nhà cung cấp vận chuyển.'
        );
      }

      // Lấy service ID phù hợp
      let serviceId = 53320; // Mặc định: Giao hàng tiêu chuẩn
      try {
        const availableServices = await this.ghnShipmentService.getServices(
          shopShippingInfo.fromDistrictId,
          deliveryAddress.districtId || 1443
        );
        if (availableServices?.data && availableServices.data.length > 0) {
          // Chọn service đầu tiên (thường là service tiêu chuẩn)
          serviceId = availableServices.data[0].service_id;
          this.logger.log('Sử dụng service ID từ API:', {
            serviceId,
            serviceName: availableServices.data[0].short_name,
            serviceTypeId: availableServices.data[0].service_type_id
          });
        }
      } catch (serviceError) {
        this.logger.warn('Không thể lấy services, sử dụng service mặc định:', serviceError.message);
      }

      // ❌ Không cần payment_type_id cho Calculate Fee vì GHN API không hỗ trợ
      // payment_type_id chỉ được sử dụng khi tạo đơn hàng, không phải khi tính phí

      // Tạo request tính phí GHN với thông tin chính xác
      const feeRequest = {
        shopId: parseInt(userConfig.shopId), // ✅ Sử dụng shop ID từ user config
        serviceId: serviceId,
        serviceTypeId: 2, // E-commerce
        fromDistrictId: shopShippingInfo.fromDistrictId,
        fromWardCode: shopShippingInfo.fromWardCode,
        toDistrictId: deliveryAddress.districtId || 1443,
        toWardCode: deliveryAddress.wardCode || '21308',
        weight: Math.max(weight, 100), // Tối thiểu 100g
        length: maxLength,
        width: maxWidth,
        height: maxHeight,
        insuranceValue: Math.min(value, 500000), // ✅ Giảm bảo hiểm xuống 500k để tiết kiệm phí
        codValue: 0
        // ❌ Không gửi payment_type_id vì GHN Calculate Fee API không hỗ trợ
      };

      this.logger.log(`GHN fee calculation request:`, {
        shopId: feeRequest.shopId,
        serviceId: feeRequest.serviceId,
        fromDistrict: shopShippingInfo.fromDistrictId,
        toDistrict: deliveryAddress.districtId,
        weight: feeRequest.weight,
        value,
        insuranceValue: feeRequest.insuranceValue,
        dimensions: `${feeRequest.length}x${feeRequest.width}x${feeRequest.height}`,
        note: 'GHN Calculate Fee API không hỗ trợ payment_type_id - chỉ áp dụng khi tạo đơn hàng',
        receiverPaysShipping: receiverPaysShipping || false
      });

      const result = await this.ghnShipmentService.calculateFee(feeRequest);

      this.logger.log(`GHN fee calculation response:`, {
        total: result.data.total,
        serviceFee: result.data.service_fee,
        insuranceFee: result.data.insurance_fee,
        pickStationFee: result.data.pick_station_fee,
        couponValue: result.data.coupon_value,
        r2sFee: result.data.r2s_fee
      });

      return {
        fee: result.data.total || 30000,
        serviceType: 'standard'
      };
    } catch (error) {
      this.logger.error(`Lỗi tính phí GHN: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Tính phí vận chuyển GHTK - Đã cải thiện với address validation
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param logisticInfo Thông tin logistics
   * @param weight Trọng lượng
   * @param value Giá trị đơn hàng
   * @param receiverPaysShipping Người nhận có trả phí vận chuyển không
   * @returns Phí vận chuyển GHTK
   */
  private async calculateGHTKFee(
    userId: number,
    shopId: number,
    logisticInfo: any,
    weight: number,
    value: number,
    receiverPaysShipping?: boolean
  ): Promise<{ fee: number; serviceType: string }> {
    try {
      // Validate và parse địa chỉ giao hàng bằng service mới
      const deliveryAddress = await this.addressValidationService.validateAndParseAddress(logisticInfo.deliveryAddress);

      if (!deliveryAddress.isValid) {
        this.logger.warn(`Delivery address parsing has low confidence: ${deliveryAddress.confidence}`, {
          address: logisticInfo.deliveryAddress,
          parsed: deliveryAddress
        });
      }

      // Lấy thông tin shop shipping từ service mới
      const shopShippingInfo = await this.shopShippingService.getGHTKShopInfo(shopId, userId);

      // Xác định is_freeship cho GHTK
      const isFreeship = receiverPaysShipping ? '0' : '1'; // '1' = sender pays (freeship), '0' = receiver pays

      // Tạo request tính phí GHTK với thông tin chính xác
      const feeRequest = {
        pickProvince: shopShippingInfo.pickProvince,
        pickDistrict: shopShippingInfo.pickDistrict,
        pickWard: shopShippingInfo.pickWard,
        province: deliveryAddress.province,
        district: deliveryAddress.district,
        ward: deliveryAddress.ward,
        weight: Math.max(weight / 1000, 0.1), // Chuyển từ gram sang kg, tối thiểu 0.1kg
        value: value,
        transport: 'road', // Đường bộ
        deliverOption: 'none',
        isFreeship: isFreeship // ✅ Thêm is_freeship
      };

      this.logger.log(`GHTK fee calculation request:`, {
        pickProvince: feeRequest.pickProvince,
        pickDistrict: feeRequest.pickDistrict,
        pickWard: feeRequest.pickWard,
        province: feeRequest.province,
        district: feeRequest.district,
        ward: feeRequest.ward,
        weight: feeRequest.weight,
        value: feeRequest.value,
        transport: feeRequest.transport,
        deliverOption: feeRequest.deliverOption,
        isFreeship: feeRequest.isFreeship,
        receiverPaysShipping: receiverPaysShipping || false
      });

      // Cập nhật config GHTK service với config của user trước khi tính phí
      await this.updateGHTKServiceConfig(userId);

      // Kiểm tra user config GHTK
      const userConfig = await this.userProviderShipmentService.getDecryptedConfig(userId, ProviderShipmentType.GHTK);
      if (!userConfig || !userConfig.token) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHTK_INVALID_CONFIG,
          'Chưa cấu hình token cho GHTK. Vui lòng cấu hình trong phần quản lý nhà cung cấp vận chuyển.'
        );
      }

      this.logger.log(`GHTK user config:`, {
        hasToken: !!userConfig.token,
        tokenLength: userConfig.token ? userConfig.token.length : 0,
        baseUrl: userConfig.baseUrl,
        isTestMode: userConfig.isTestMode
      });

      const result = await this.ghtkShipmentService.calculateFee(feeRequest);

      this.logger.log(`GHTK fee calculation response:`, {
        success: result.success,
        fee: result.fee.fee,
        insuranceFee: result.fee.insuranceFee,
        deliveryType: result.fee.deliveryType,
        extFees: result.fee.extFees,
        delivery: result.fee.delivery
      });

      return {
        fee: result.fee.fee || 25000,
        serviceType: result.fee.deliveryType || 'standard'
      };
    } catch (error) {
      this.logger.error(`Lỗi tính phí GHTK: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Parse địa chỉ để lấy thông tin tỉnh/quận/phường
   * @param address Địa chỉ đầy đủ
   * @returns Thông tin địa chỉ đã parse
   */
  private parseAddress(address: string): {
    province?: string;
    district?: string;
    ward?: string;
    districtId?: number;
    wardCode?: string;
  } {
    this.logger.log(`Parsing address: "${address}"`);

    if (!address) {
      this.logger.warn('Address is empty or null');
      return {};
    }

    const addressParts = address.split(',').map(part => part.trim());
    this.logger.log(`Address parts:`, addressParts);

    // Tìm tỉnh/thành phố
    let province = '';
    let district = '';
    let ward = '';

    for (const part of addressParts) {
      if (part.toLowerCase().includes('hồ chí minh') || part.toLowerCase().includes('tp.hcm')) {
        province = 'Hồ Chí Minh';
      } else if (part.toLowerCase().includes('hà nội')) {
        province = 'Hà Nội';
      } else if (part.toLowerCase().includes('đà nẵng')) {
        province = 'Đà Nẵng';
      } else if (part.toLowerCase().includes('quận') || part.toLowerCase().includes('huyện')) {
        district = part;
      } else if (part.toLowerCase().includes('phường') || part.toLowerCase().includes('xã')) {
        ward = part;
      }
    }

    // Mapping đơn giản cho district ID và ward code (có thể mở rộng)
    let districtId = 1442; // Quận 1, TP.HCM mặc định
    let wardCode = '21211'; // Phường Bến Nghé mặc định

    const result = {
      province: province || 'Hồ Chí Minh',
      district: district || 'Quận 1',
      ward: ward || 'Phường Bến Nghé',
      districtId,
      wardCode
    };

    this.logger.log(`Parsed address result:`, result);
    return result;
  }

  /**
   * Tách đường/phố từ địa chỉ đầy đủ cho GHTK
   * @param fullAddress Địa chỉ đầy đủ
   * @returns Đường/phố
   */
  private extractStreetFromAddress(fullAddress: string): string {
    if (!fullAddress) return '';

    // Tách địa chỉ theo dấu phẩy
    const parts = fullAddress.split(',').map(part => part.trim());

    // Với địa chỉ Google Maps như "FHV8+9F6, Tản Đà, Hương Sơ, Huế, Thành phố Huế, Việt Nam"
    // Lấy 2 phần đầu tiên làm đường/phố
    let street = '';
    if (parts.length >= 2) {
      // Kết hợp 2 phần đầu: "FHV8+9F6, Tản Đà"
      street = `${parts[0]}, ${parts[1]}`;
    } else if (parts.length >= 1) {
      // Chỉ có 1 phần
      street = parts[0];
    }

    // Nếu street vẫn trống, dùng fallback
    if (!street || street.trim() === '') {
      street = 'Địa chỉ không rõ';
    }

    this.logger.log(`Extracted street from "${fullAddress}": "${street}"`);
    return street;
  }

  /**
   * Submit đơn hàng đến đơn vị vận chuyển
   * @param order Đơn hàng
   * @param carrier Đơn vị vận chuyển
   * @param logisticInfo Thông tin logistics
   * @param productInfo Thông tin sản phẩm
   */
  private async submitOrderToShippingProvider(
    order: UserOrder,
    carrier: string,
    logisticInfo: any,
    productInfo: any
  ): Promise<void> {
    try {
      this.logger.log(`Submit đơn hàng ${order.id} đến ${carrier}`);

      const addressInfo = this.parseAddress(logisticInfo.deliveryAddress);
      const totalWeight = this.calculateTotalWeight(productInfo); // Gram cho GHN
      const totalWeightKg = this.calculateTotalWeightForGHTK(productInfo); // Kg cho GHTK
      const totalValue = this.calculateTotalValue(productInfo);

      if (carrier === 'GHN') {
        await this.submitToGHN(order, logisticInfo, addressInfo, productInfo, totalWeight);
      } else if (carrier === 'GHTK') {
        await this.submitToGHTK(order, logisticInfo, addressInfo, productInfo, totalWeightKg, totalValue);
      }

      this.logger.log(`Đã submit đơn hàng ${order.id} đến ${carrier} thành công`);
    } catch (error) {
      this.logger.error(`Lỗi khi submit đơn hàng ${order.id} đến ${carrier}:`, error);
      throw error;
    }
  }

  /**
   * Submit đơn hàng đến GHN
   */
  private async submitToGHN(
    order: UserOrder,
    logisticInfo: any,
    addressInfo: any,
    productInfo: any,
    totalWeight: number
  ): Promise<void> {
    try {
      // Lấy setting receiverPaysShipping từ User Provider Shipment
      let receiverPaysShipping = false; // Mặc định false (người gửi trả)
      try {
        const userProviderConfig = await this.userProviderShipmentService.findByTypeAndUserId(ProviderShipmentType.GHN, order.userId);
        if (userProviderConfig && userProviderConfig.length > 0) {
          receiverPaysShipping = userProviderConfig[0].receiverPaysShipping;
          this.logger.log(`GHN Create Order - Sử dụng receiverPaysShipping từ User Provider Shipment: ${receiverPaysShipping} (${receiverPaysShipping ? 'người nhận trả' : 'người gửi trả'})`);
        } else {
          this.logger.log(`GHN Create Order - Không tìm thấy User Provider Shipment, sử dụng mặc định: false (người gửi trả)`);
        }
      } catch (error) {
        this.logger.warn(`GHN Create Order - Lỗi khi lấy setting receiverPaysShipping: ${error.message}`);
        receiverPaysShipping = false; // Mặc định false (người gửi trả)
      }

      // Xác định payment type cho GHN
      const paymentTypeId = receiverPaysShipping ? 2 : 1; // 1 = sender pays, 2 = receiver pays

      // Tạo request cho GHN
      const ghnRequest = {
        shopId: parseInt(process.env.GHN_SHOP_ID || '0'),
        clientOrderCode: `ORDER_${order.id}_${Date.now()}`,
        toName: logisticInfo.recipientName || 'Khách hàng',
        toPhone: logisticInfo.recipientPhone || '**********',
        toAddress: logisticInfo.deliveryAddress,
        toWardCode: addressInfo.wardCode || '21211',
        toDistrictId: addressInfo.districtId || 1442,
        fromName: await this.userShopInfoService.getShopName(order.userId),
        fromPhone: await this.userShopInfoService.getShopPhone(order.userId),
        fromAddress: await this.userShopInfoService.getShopAddress(order.userId),
        fromWardCode: '21211',
        fromDistrictId: 1442,
        fromWardName: await this.userShopInfoService.getShopWard(order.userId),
        fromDistrictName: await this.userShopInfoService.getShopDistrict(order.userId),
        fromProvinceName: await this.userShopInfoService.getShopProvince(order.userId),
        returnPhone: await this.userShopInfoService.getShopPhone(order.userId),
        returnAddress: await this.userShopInfoService.getShopAddress(order.userId),
        returnDistrictId: 1442,
        returnWardCode: '21211',
        codAmount: logisticInfo.codAmount || 0,
        content: `Đơn hàng #${order.id}`,
        weight: Math.max(totalWeight, 100),
        length: 20,
        width: 15,
        height: 10,
        serviceTypeId: 2, // E-commerce
        serviceId: 53320, // Dịch vụ tiêu chuẩn
        paymentTypeId: paymentTypeId, // ✅ Sử dụng setting từ User Provider Shipment
        requiredNote: 'KHONGCHOXEMHANG',
        note: logisticInfo.shippingNote || '',
        items: productInfo.products?.map((product: any) => ({
          name: product.name,
          code: product.productId?.toString(),
          quantity: product.quantity,
          price: product.unitPrice,
          weight: product.weight || 100
        })) || []
      };

      // Cập nhật config GHN service với config của user trước khi tạo đơn hàng
      await this.updateGHNServiceConfig(order.userId);

      const result = await this.ghnShipmentService.createOrder(ghnRequest);

      // Cập nhật logistic info với tracking number
      await this.updateOrderLogisticInfo(order.id, {
        trackingNumber: result.data.order_code,
        label: result.data.order_code, // GHN sử dụng order_code làm label
        estimatedDeliveryTime: result.data.expected_delivery_time,
        shippingProvider: 'GHN',
        shippingStatus: 'submitted'
      });

      this.logger.log(`Đã tạo vận đơn GHN: ${result.data.order_code}`);
    } catch (error) {
      this.logger.error('Lỗi khi submit đến GHN:', error);
      throw error;
    }
  }

  /**
   * Submit đơn hàng đến GHTK
   */
  private async submitToGHTK(
    order: UserOrder,
    logisticInfo: any,
    addressInfo: any,
    productInfo: any,
    totalWeightKg: number,
    totalValue: number
  ): Promise<void> {
    try {
      this.logger.log(`Bắt đầu submit đơn hàng ${order.id} đến GHTK`, {
        orderId: order.id,
        userId: order.userId,
        logisticInfo,
        addressInfo,
        totalWeightKg,
        totalValue
      });

      // Lấy setting receiverPaysShipping từ User Provider Shipment
      let receiverPaysShipping = false; // Mặc định false (người gửi trả)
      try {
        const userProviderConfig = await this.userProviderShipmentService.findByTypeAndUserId(ProviderShipmentType.GHTK, order.userId);
        if (userProviderConfig && userProviderConfig.length > 0) {
          receiverPaysShipping = userProviderConfig[0].receiverPaysShipping;
          this.logger.log(`GHTK Create Order - Sử dụng receiverPaysShipping từ User Provider Shipment: ${receiverPaysShipping} (${receiverPaysShipping ? 'người nhận trả' : 'người gửi trả'})`);
        } else {
          this.logger.log(`GHTK Create Order - Không tìm thấy User Provider Shipment, sử dụng mặc định: false (người gửi trả)`);
        }
      } catch (error) {
        this.logger.warn(`GHTK Create Order - Lỗi khi lấy setting receiverPaysShipping: ${error.message}`);
        receiverPaysShipping = false; // Mặc định false (người gửi trả)
      }

      // Xác định is_freeship cho GHTK
      const isFreeship = receiverPaysShipping ? '0' : '1'; // '1' = sender pays (freeship), '0' = receiver pays

      // Cập nhật config GHTK service với config của user
      await this.updateGHTKServiceConfig(order.userId);
      // Lấy thông tin customer để có địa chỉ chính xác
      const customer = await this.userConvertCustomerRepository.findById(order.userConvertCustomerId);
      if (!customer) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND,
          'Không tìm thấy thông tin khách hàng'
        );
      }

      this.logger.log('Customer info for GHTK:', {
        customerId: customer.id,
        name: customer.name,
        phone: customer.phone,
        address: customer.address
      });

      // Lấy thông tin sản phẩm từ database
      const productIds = productInfo.products?.map((p: any) => p.productId) || [];
      const products = await this.userProductRepository.findByIds(productIds);

      // Tạo thông tin pickup từ shop info
      const pickupInfo = {
        name: await this.userShopInfoService.getShopName(order.userId),
        address: await this.userShopInfoService.getShopAddress(order.userId),
        province: await this.userShopInfoService.getShopProvince(order.userId),
        district: await this.userShopInfoService.getShopDistrict(order.userId),
        ward: await this.userShopInfoService.getShopWard(order.userId),
        tel: await this.userShopInfoService.getShopPhone(order.userId)
      };

      this.logger.log('Pickup info for GHTK:', pickupInfo);

      // Sử dụng GHTK Integration Helper để tạo request
      const ghtkRequestRaw = this.ghtkIntegrationHelper.convertOrderToGHTKFormat(
        order,
        customer,
        products,
        pickupInfo
      );

      // Chuyển đổi từ IGHTKCreateOrderRequest sang CreateGHTKOrderRequestDto
      const ghtkRequest = {
        products: ghtkRequestRaw.products.map(product => ({
          name: product.name,
          price: product.price,
          weight: product.weight,
          quantity: product.quantity,
          productCode: product.product_code
        })),
        order: {
          id: ghtkRequestRaw.order.id,
          pickName: ghtkRequestRaw.order.pick_name,
          pickAddress: ghtkRequestRaw.order.pick_address,
          pickProvince: ghtkRequestRaw.order.pick_province,
          pickDistrict: ghtkRequestRaw.order.pick_district,
          pickWard: ghtkRequestRaw.order.pick_ward,
          pickTel: ghtkRequestRaw.order.pick_tel,
          name: ghtkRequestRaw.order.name,
          address: ghtkRequestRaw.order.address,
          province: ghtkRequestRaw.order.province,
          district: ghtkRequestRaw.order.district,
          ward: ghtkRequestRaw.order.ward,
          hamlet: ghtkRequestRaw.order.hamlet,
          tel: ghtkRequestRaw.order.tel,
          value: ghtkRequestRaw.order.value,
          pickMoney: logisticInfo.codAmount || 0,
          note: logisticInfo.shippingNote || '',
          transport: ghtkRequestRaw.order.transport || 'road',
          pickOption: ghtkRequestRaw.order.pick_option || 'cod',
          deliverOption: ghtkRequestRaw.order.deliver_option || 'none',
          isFreeship: isFreeship ? '1' : '0'
        }
      };

      this.logger.log(`GHTK request được tạo cho đơn hàng ${order.id} bằng Integration Helper:`, {
        orderId: order.id,
        ghtkRequest: JSON.stringify(ghtkRequest, null, 2),
        receiverPaysShipping,
        isFreeship,
        customerInfo: {
          name: customer.name,
          phone: customer.phone,
          address: customer.address
        },
        logisticInfo: {
          deliveryAddress: logisticInfo.deliveryAddress,
          recipientName: logisticInfo.recipientName,
          recipientPhone: logisticInfo.recipientPhone
        },
        finalGHTKAddress: {
          name: ghtkRequest.order.name,
          address: ghtkRequest.order.address,
          province: ghtkRequest.order.province,
          district: ghtkRequest.order.district,
          ward: ghtkRequest.order.ward,
          hamlet: ghtkRequest.order.hamlet,
          tel: ghtkRequest.order.tel
        },
        rawGHTKData: {
          rawAddress: ghtkRequestRaw.order.address,
          rawProvince: ghtkRequestRaw.order.province,
          rawDistrict: ghtkRequestRaw.order.district,
          rawWard: ghtkRequestRaw.order.ward
        }
      });

      const result = await this.ghtkShipmentService.createOrder(ghtkRequest);

      // Cập nhật logistic info với tracking number
      await this.updateOrderLogisticInfo(order.id, {
        trackingNumber: result.order.trackingId,
        label: result.order.label,
        estimatedDeliveryTime: result.order.estimatedDeliverTime,
        shippingProvider: 'GHTK',
        shippingStatus: 'submitted'
      });

      this.logger.log(`Đã tạo vận đơn GHTK: ${result.order.trackingId}`);
    } catch (error) {
      this.logger.error('Lỗi khi submit đến GHTK:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin logistics của đơn hàng
   * @param orderId ID đơn hàng
   * @param updateData Dữ liệu cập nhật
   */
  private async updateOrderLogisticInfo(orderId: number, updateData: any): Promise<void> {
    try {
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new Error(`Không tìm thấy đơn hàng ${orderId}`);
      }

      const updatedLogisticInfo = {
        ...order.logisticInfo,
        ...updateData,
        updatedAt: Date.now()
      };

      await this.userOrderRepository.updateOrder(orderId, order.userId, {
        logisticInfo: updatedLogisticInfo
      });

      this.logger.log(`Đã cập nhật logistic info cho đơn hàng ${orderId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật logistic info cho đơn hàng ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   * @param userId ID người dùng
   * @returns Thống kê trạng thái đơn hàng và vận chuyển
   */
  async getOrderStatusStats(userId: number): Promise<UserOrderStatusResponseDto> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${userId}`);

      // Lấy thống kê từ repository
      const stats = await this.userOrderRepository.getOrderStatusStats(userId);

      // Debug logs
      this.logger.log(`Raw stats from repository:`, {
        totalOrders: stats.totalOrders,
        ordersWithShipping: stats.ordersWithShipping,
        ordersWithoutShipping: stats.ordersWithoutShipping,
        orderStatus: stats.orderStatus,
        shippingStatus: stats.shippingStatus,
      });

      // Khởi tạo các giá trị mặc định cho order status (tất cả đơn hàng)
      const orderStatusStats: OrderStatusStatsDto = {
        pending: stats.orderStatus[OrderStatusEnum.PENDING] || 0,
        confirmed: stats.orderStatus[OrderStatusEnum.CONFIRMED] || 0,
        processing: stats.orderStatus[OrderStatusEnum.PROCESSING] || 0,
        completed: stats.orderStatus[OrderStatusEnum.COMPLETED] || 0,
        cancelled: stats.orderStatus[OrderStatusEnum.CANCELLED] || 0,
        total: stats.totalOrders, // Sử dụng tổng số đơn hàng thực tế
      };

      // Khởi tạo các giá trị mặc định cho shipping status (tất cả trạng thái từ enum đã làm sạch)
      const shippingStatusStats: ShippingStatusStatsDto = {
        pending: stats.shippingStatus[ShippingStatusEnum.PENDING] || 0,
        preparing: stats.shippingStatus[ShippingStatusEnum.PREPARING] || 0,
        shipped: stats.shippingStatus[ShippingStatusEnum.SHIPPED] || 0,
        inTransit: stats.shippingStatus[ShippingStatusEnum.IN_TRANSIT] || 0,
        sorting: stats.shippingStatus[ShippingStatusEnum.SORTING] || 0,
        delivered: stats.shippingStatus[ShippingStatusEnum.DELIVERED] || 0,
        deliveryFailed: stats.shippingStatus[ShippingStatusEnum.DELIVERY_FAILED] || 0,
        returning: stats.shippingStatus[ShippingStatusEnum.RETURNING] || 0,
        cancelled: stats.shippingStatus[ShippingStatusEnum.CANCELLED] || 0,
        total: stats.ordersWithShipping, // Chỉ đếm đơn hàng có vận chuyển
      };

      this.logger.log(`Final calculated stats for userId=${userId}:`, {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      });

      // Tạo response DTO
      const response: UserOrderStatusResponseDto = {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      };

      return plainToInstance(UserOrderStatusResponseDto, response, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Tracking trạng thái đơn hàng từ đơn vị vận chuyển
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @returns Thông tin tracking
   */
  async trackOrder(orderId: number, userId: number): Promise<any> {
    try {
      this.logger.log(`Tracking đơn hàng ${orderId} cho userId=${userId}`);

      // Lấy thông tin đơn hàng
      const order = await this.userOrderRepository.findById(orderId);
      if (!order || order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Không tìm thấy đơn hàng hoặc bạn không có quyền truy cập'
        );
      }

      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo?.trackingNumber || !logisticInfo?.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
          'Đơn hàng chưa có thông tin vận chuyển'
        );
      }

      // Validate cấu hình provider của user trước khi tracking
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      let trackingResult: any;
      if (logisticInfo.shippingProvider === 'GHN') {
        // Cập nhật config GHN service với config của user
        await this.updateGHNServiceConfig(userId);
        trackingResult = await this.ghnShipmentService.getOrderInfo(logisticInfo.trackingNumber);
      } else if (logisticInfo.shippingProvider === 'GHTK') {
        // Cập nhật config GHTK service với config của user
        await this.updateGHTKServiceConfig(userId);
        trackingResult = await this.ghtkShipmentService.getOrderStatus(logisticInfo.trackingNumber);
      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
          'Đơn vị vận chuyển không được hỗ trợ'
        );
      }

      // Cập nhật trạng thái nếu có thay đổi
      await this.updateShippingStatusFromTracking(order, trackingResult, logisticInfo.shippingProvider);

      return {
        orderId: order.id,
        trackingNumber: logisticInfo.trackingNumber,
        carrier: logisticInfo.shippingProvider,
        status: trackingResult.data || trackingResult.order,
        lastUpdated: Date.now()
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tracking đơn hàng ${orderId}:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi tracking đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật trạng thái vận chuyển từ kết quả tracking
   * @param order Đơn hàng
   * @param trackingResult Kết quả tracking
   * @param carrier Đơn vị vận chuyển
   */
  private async updateShippingStatusFromTracking(
    order: UserOrder,
    trackingResult: any,
    carrier: string
  ): Promise<void> {
    try {
      let newShippingStatus: ShippingStatusEnum | null = null;
      let newOrderStatus: OrderStatusEnum | null = null;

      if (carrier === 'GHN') {
        // Mapping trạng thái GHN sang hệ thống
        const ghnStatus = trackingResult.data?.status;
        switch (ghnStatus) {
          case 'ready_to_pick':
          case 'picking':
            newShippingStatus = ShippingStatusEnum.PREPARING;
            break;
          case 'picked':
          case 'storing':
          case 'transporting':
            newShippingStatus = ShippingStatusEnum.IN_TRANSIT;
            break;
          case 'sorting':
            newShippingStatus = ShippingStatusEnum.SORTING;
            break;
          case 'delivering':
          case 'money_collect_delivering':
            newShippingStatus = ShippingStatusEnum.IN_TRANSIT;
            break;
          case 'delivered':
            newShippingStatus = ShippingStatusEnum.DELIVERED;
            newOrderStatus = OrderStatusEnum.COMPLETED;
            break;
          case 'delivery_fail':
            newShippingStatus = ShippingStatusEnum.DELIVERY_FAILED;
            break;
          case 'waiting_to_return':
          case 'return':
            newShippingStatus = ShippingStatusEnum.RETURNING;
            break;
          case 'cancel':
            newShippingStatus = ShippingStatusEnum.CANCELLED;
            newOrderStatus = OrderStatusEnum.CANCELLED;
            break;
        }
      } else if (carrier === 'GHTK') {
        // Mapping trạng thái GHTK sang hệ thống
        const ghtkStatusId = trackingResult.order?.statusId;
        switch (ghtkStatusId) {
          case 1: // Chờ lấy hàng
          case 2: // Đã lấy hàng
            newShippingStatus = ShippingStatusEnum.PREPARING;
            break;
          case 3: // Đang vận chuyển
          case 4: // Đang giao hàng
            newShippingStatus = ShippingStatusEnum.IN_TRANSIT;
            break;
          case 5: // Đã giao hàng
            newShippingStatus = ShippingStatusEnum.DELIVERED;
            newOrderStatus = OrderStatusEnum.COMPLETED;
            break;
          case 6: // Giao hàng thất bại
            newShippingStatus = ShippingStatusEnum.DELIVERY_FAILED;
            break;
          case 7: // Đang hoàn hàng
          case 8: // Đã hoàn hàng
            newShippingStatus = ShippingStatusEnum.RETURNING;
            break;
          case 9: // Hủy đơn hàng
            newShippingStatus = ShippingStatusEnum.CANCELLED;
            newOrderStatus = OrderStatusEnum.CANCELLED;
            break;
        }
      }

      // Cập nhật trạng thái nếu có thay đổi
      if (newShippingStatus && newShippingStatus !== order.shippingStatus) {
        const updateData: any = { shippingStatus: newShippingStatus };
        if (newOrderStatus && newOrderStatus !== order.orderStatus) {
          updateData.orderStatus = newOrderStatus;
        }

        await this.userOrderRepository.updateOrder(order.id, order.userId, updateData);
        this.logger.log(`Đã cập nhật trạng thái đơn hàng ${order.id}: ${newShippingStatus}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái từ tracking:`, error);
    }
  }

  /**
   * Xử lý webhook từ đơn vị vận chuyển
   * @param webhookData Dữ liệu webhook
   * @param carrier Đơn vị vận chuyển
   */
  async handleShippingWebhook(webhookData: any, carrier: string): Promise<void> {
    try {
      this.logger.log(`Xử lý webhook từ ${carrier}`, webhookData);

      let trackingNumber: string;
      let orderId: number | null = null;

      if (carrier === 'GHN') {
        trackingNumber = webhookData.OrderCode;
        // Tìm đơn hàng theo tracking number
        const order = await this.findOrderByTrackingNumber(trackingNumber);
        if (order) {
          orderId = order.id;
          await this.updateShippingStatusFromTracking(order, { data: webhookData }, carrier);
        }
      } else if (carrier === 'GHTK') {
        trackingNumber = webhookData.label_id;
        // Tìm đơn hàng theo tracking number
        const order = await this.findOrderByTrackingNumber(trackingNumber);
        if (order) {
          orderId = order.id;
          await this.updateShippingStatusFromTracking(order, { order: webhookData }, carrier);
        }
      }

      this.logger.log(`Đã xử lý webhook ${carrier} cho đơn hàng ${orderId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý webhook ${carrier}:`, error);
    }
  }

  /**
   * Tìm đơn hàng theo tracking number
   * @param trackingNumber Mã vận đơn
   * @returns Đơn hàng
   */
  private async findOrderByTrackingNumber(trackingNumber: string): Promise<UserOrder | null> {
    try {
      // Tìm trong repository (cần implement method này trong repository)
      return await this.userOrderRepository.findByTrackingNumber(trackingNumber);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm đơn hàng theo tracking number ${trackingNumber}:`, error);
      return null;
    }
  }

  /**
   * In đơn hàng theo đơn vị vận chuyển
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param baseUrl Base URL cho download link
   * @param printOptions Tùy chọn in (khổ giấy, hướng in, format)
   * @returns Thông tin in đơn hàng
   */
  // Cache để lưu PDF buffers tạm thời
  private pdfCache = new Map<string, { buffer: Buffer; timestamp: number; options: any }>();

  // Cache để lưu download tokens tạm thời
  private downloadTokens = new Map<string, { orderId: number; userId: number; options: any; timestamp: number }>();

  async printOrder(orderId: number, userId: number, printOptions?: any): Promise<any> {
    try {
      this.logger.log(`In đơn hàng ${orderId} cho user ${userId}`);

      // Tìm đơn hàng và validate quyền truy cập
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại'
        );
      }

      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          'Bạn không có quyền truy cập đơn hàng này'
        );
      }

      // Kiểm tra thông tin vận chuyển
      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo || !logisticInfo.trackingNumber || !logisticInfo.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO,
          'Đơn hàng không có thông tin vận chuyển để in'
        );
      }

      // Validate cấu hình provider của user trước khi in
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      // Set default print options
      const options = {
        paperSize: printOptions?.paperSize || 'A6',
        orientation: printOptions?.orientation || 'portrait',
        ghnFormat: printOptions?.ghnFormat || 'A5'
      };

      let printResult: any;
      const currentTime = Date.now();

      if (logisticInfo.shippingProvider === 'GHN') {
        // Cập nhật config GHN service với config của user
        await this.updateGHNServiceConfig(userId);

        // In đơn hàng GHN - trả về token
        const ghnResult = await this.ghnShipmentService.printOrder([logisticInfo.trackingNumber]);

        // Debug log để xem cấu trúc response
        this.logger.log('GHN printOrder response:', JSON.stringify(ghnResult, null, 2));

        // Lấy base URL từ config của GHN service để đảm bảo đúng môi trường (test/production)
        const ghnConfig = this.ghnShipmentService.getConfig();
        const ghnBaseUrl = ghnConfig.baseUrl || 'https://online-gateway.ghn.vn';

        // Xử lý token từ GHN response
        const tokenValue = typeof ghnResult.data === 'string' ? ghnResult.data : ghnResult.data?.token || ghnResult.data;
        this.logger.log('Extracted token value:', tokenValue);

        // Xác định print URL dựa trên format
        let printEndpoint = '/a5/public-api/printA5'; // Default A5
        let formatDescription = 'A5';

        if (options.ghnFormat === '80x80') {
          printEndpoint = '/a5/public-api/print80x80';
          formatDescription = '80x80mm (Thermal)';
        } else if (options.ghnFormat === '52x70') {
          printEndpoint = '/a5/public-api/print52x70';
          formatDescription = '52x70mm (Thermal)';
        }

        const printData = {
          token: tokenValue,
          printUrl: `${ghnBaseUrl}${printEndpoint}?token=${tokenValue}`,
          instructions: `Sử dụng URL này để in đơn hàng khổ ${formatDescription}. Token có hiệu lực trong 24 giờ.`,
          format: options.ghnFormat
        };

        printResult = {
          orderId: orderId.toString(),
          carrier: 'GHN',
          trackingNumber: logisticInfo.trackingNumber,
          printType: 'token',
          printData,
          createdAt: currentTime
        };

      } else if (logisticInfo.shippingProvider === 'GHTK') {
        // Cập nhật config GHTK service với config của user
        await this.updateGHTKServiceConfig(userId);
        // In đơn hàng GHTK - trả về thông tin PDF
        const ghtkResult = await this.ghtkShipmentService.printLabel(logisticInfo.trackingNumber, {
          trackingOrder: logisticInfo.trackingNumber, // Required field trong DTO
          paperSize: options.paperSize,
          original: options.orientation
        });

        // Lưu PDF vào cache với key unique
        const cacheKey = `${orderId}_${userId}_${options.paperSize}_${options.orientation}`;
        this.pdfCache.set(cacheKey, {
          buffer: ghtkResult,
          timestamp: Date.now(),
          options: options
        });

        const printData = {
          fileName: `GHTK_Label_${logisticInfo.trackingNumber}.pdf`,
          fileSize: ghtkResult.length,
          paperSize: options.paperSize,
          orientation: options.orientation
        };

        printResult = {
          orderId: orderId.toString(),
          carrier: 'GHTK',
          trackingNumber: logisticInfo.trackingNumber,
          printType: 'pdf',
          printData,
          pdfBuffer: ghtkResult, // Trả về buffer để controller serve trực tiếp
          createdAt: currentTime
        };

      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_PRINT_FAILED,
          'Đơn vị vận chuyển không được hỗ trợ'
        );
      }

      this.logger.log(`In đơn hàng ${orderId} thành công với ${logisticInfo.shippingProvider}`);
      return printResult;

    } catch (error) {
      this.logger.error(`Lỗi khi in đơn hàng ${orderId}:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_PRINT_FAILED,
        `Lỗi khi in đơn hàng: ${error.message}`
      );
    }
  }


  /**
   * Tracking đơn hàng theo ID (public method cho API)
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param updateStatus Có cập nhật trạng thái trong hệ thống hay không
   * @returns Thông tin tracking
   */
  async trackOrderById(orderId: number, userId: number, updateStatus: boolean = true): Promise<any> {
    try {
      this.logger.log(`Tracking đơn hàng ${orderId} cho user ${userId}`);
      return await this.trackOrder(orderId, userId);
    } catch (error) {
      this.logger.error(`Lỗi khi tracking đơn hàng ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Hủy đơn hàng theo ID (public method cho API)
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param reason Lý do hủy đơn
   * @returns Thông tin hủy đơn
   */
  async cancelOrderById(orderId: number, userId: number, reason?: string): Promise<any> {
    try {
      this.logger.log(`Hủy đơn hàng ${orderId} cho user ${userId}`);

      // Tìm đơn hàng và validate quyền truy cập
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại'
        );
      }

      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          'Bạn không có quyền truy cập đơn hàng này'
        );
      }

      // Kiểm tra trạng thái đơn hàng có thể hủy không
      if (order.orderStatus === OrderStatusEnum.COMPLETED || order.orderStatus === OrderStatusEnum.CANCELLED) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          'Không thể hủy đơn hàng đã hoàn thành hoặc đã hủy'
        );
      }

      // Kiểm tra thông tin vận chuyển
      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo || !logisticInfo.trackingNumber || !logisticInfo.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO,
          'Đơn hàng không có thông tin vận chuyển để hủy'
        );
      }

      // Validate cấu hình provider của user trước khi hủy
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      let cancelResult: any;
      const currentTime = Date.now();

      if (logisticInfo.shippingProvider === 'GHN') {
        // Cập nhật config GHN service với config của user
        await this.updateGHNServiceConfig(userId);
        cancelResult = await this.ghnShipmentService.cancelOrder([logisticInfo.trackingNumber]);
      } else if (logisticInfo.shippingProvider === 'GHTK') {
        // Cập nhật config GHTK service với config của user
        await this.updateGHTKServiceConfig(userId);
        cancelResult = await this.ghtkShipmentService.cancelOrder(logisticInfo.trackingNumber);
      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          'Đơn vị vận chuyển không được hỗ trợ'
        );
      }

      // Cập nhật trạng thái đơn hàng trong database
      await this.userOrderRepository.updateOrder(orderId, userId, {
        orderStatus: OrderStatusEnum.CANCELLED,
        shippingStatus: ShippingStatusEnum.CANCELLED
      });

      this.logger.log(`Hủy đơn hàng ${orderId} thành công với ${logisticInfo.shippingProvider}`);

      return {
        orderId,
        trackingNumber: logisticInfo.trackingNumber,
        carrier: logisticInfo.shippingProvider,
        success: true,
        carrierResponse: cancelResult,
        cancelledAt: currentTime,
        reason: reason || 'Không có lý do',
        notes: `Đơn hàng đã được hủy thành công trên hệ thống ${logisticInfo.shippingProvider}`
      };

    } catch (error) {
      this.logger.error(`Lỗi khi hủy đơn hàng ${orderId}:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi hủy đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Hoàn đơn hàng theo ID (public method cho API)
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param reason Lý do hoàn đơn
   * @returns Thông tin hoàn đơn
   */
  async returnOrderById(orderId: number, userId: number, reason?: string): Promise<any> {
    try {
      this.logger.log(`Hoàn đơn hàng ${orderId} cho user ${userId}`);

      // Tìm đơn hàng và validate quyền truy cập
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại'
        );
      }

      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          'Bạn không có quyền truy cập đơn hàng này'
        );
      }

      // Kiểm tra trạng thái đơn hàng có thể hoàn không
      if (order.orderStatus === OrderStatusEnum.CANCELLED) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          'Không thể hoàn đơn hàng đã hủy'
        );
      }

      // Kiểm tra thông tin vận chuyển
      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo || !logisticInfo.trackingNumber || !logisticInfo.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO,
          'Đơn hàng không có thông tin vận chuyển để hoàn'
        );
      }

      // Validate cấu hình provider của user trước khi hoàn
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      let returnResult: any;
      const currentTime = Date.now();

      if (logisticInfo.shippingProvider === 'GHN') {
        // Cập nhật config GHN service với config của user
        await this.updateGHNServiceConfig(userId);
        returnResult = await this.ghnShipmentService.returnOrder([logisticInfo.trackingNumber]);
      } else if (logisticInfo.shippingProvider === 'GHTK') {
        // Cập nhật config GHTK service với config của user
        await this.updateGHTKServiceConfig(userId);
        // GHTK không có API riêng cho return, sử dụng cancel
        returnResult = await this.ghtkShipmentService.cancelOrder(logisticInfo.trackingNumber);
      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          'Đơn vị vận chuyển không được hỗ trợ'
        );
      }

      // Cập nhật trạng thái đơn hàng trong database
      await this.userOrderRepository.updateOrder(orderId, userId, {
        shippingStatus: ShippingStatusEnum.RETURNING
      });

      this.logger.log(`Hoàn đơn hàng ${orderId} thành công với ${logisticInfo.shippingProvider}`);

      return {
        orderId,
        trackingNumber: logisticInfo.trackingNumber,
        carrier: logisticInfo.shippingProvider,
        success: true,
        carrierResponse: returnResult,
        returnedAt: currentTime,
        reason: reason || 'Không có lý do',
        notes: `Đơn hàng đã được yêu cầu hoàn trả thành công`
      };

    } catch (error) {
      this.logger.error(`Lỗi khi hoàn đơn hàng ${orderId}:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi hoàn đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Giao lại đơn hàng theo ID (chỉ GHN) (public method cho API)
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param notes Ghi chú cho việc giao lại
   * @returns Thông tin giao lại đơn
   */
  async deliveryAgainOrderById(orderId: number, userId: number, notes?: string): Promise<any> {
    try {
      this.logger.log(`Giao lại đơn hàng ${orderId} cho user ${userId}`);

      // Tìm đơn hàng và validate quyền truy cập
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại'
        );
      }

      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          'Bạn không có quyền truy cập đơn hàng này'
        );
      }

      // Kiểm tra thông tin vận chuyển
      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo || !logisticInfo.trackingNumber || !logisticInfo.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO,
          'Đơn hàng không có thông tin vận chuyển'
        );
      }

      // Chỉ GHN hỗ trợ delivery again
      if (logisticInfo.shippingProvider !== 'GHN') {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          'Chỉ đơn hàng GHN mới hỗ trợ tính năng giao lại'
        );
      }

      // Validate cấu hình provider của user trước khi giao lại
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      // Cập nhật config GHN service với config của user
      await this.updateGHNServiceConfig(userId);
      const deliveryAgainResult = await this.ghnShipmentService.deliveryAgain([logisticInfo.trackingNumber]);

      const currentTime = Date.now();

      this.logger.log(`Giao lại đơn hàng ${orderId} thành công với GHN`);

      return {
        orderId,
        trackingNumber: logisticInfo.trackingNumber,
        carrier: 'GHN',
        success: true,
        carrierResponse: deliveryAgainResult,
        deliveryAgainAt: currentTime,
        notes: notes || 'Yêu cầu giao lại đơn hàng'
      };

    } catch (error) {
      this.logger.error(`Lỗi khi giao lại đơn hàng ${orderId}:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi giao lại đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * In đơn hàng theo ID (public method cho API)
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @param printOptions Tùy chọn in
   * @returns Thông tin in đơn hàng
   */
  async printOrderById(orderId: number, userId: number, printOptions?: any): Promise<any> {
    try {
      this.logger.log(`In đơn hàng ${orderId} cho user ${userId}`);
      return await this.printOrder(orderId, userId, printOptions);
    } catch (error) {
      this.logger.error(`Lỗi khi in đơn hàng ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * In đơn hàng với cài đặt tự động xác định loại vận chuyển và khổ giấy
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @returns Thông tin in đơn hàng
   */
  async printOrderAutoFormat(orderId: number, userId: number): Promise<any> {
    try {
      this.logger.log(`In đơn hàng tự động ${orderId} cho user ${userId}`);
      
      // Tìm đơn hàng và validate quyền truy cập
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại'
        );
      }

      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          'Bạn không có quyền truy cập đơn hàng này'
        );
      }

      // Kiểm tra thông tin vận chuyển
      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo || !logisticInfo.trackingNumber || !logisticInfo.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO,
          'Đơn hàng không có thông tin vận chuyển để in'
        );
      }

      // Validate cấu hình provider của user trước khi in
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      // Xác định cài đặt in dựa vào đơn vị vận chuyển
      const printOptions = {
        paperSize: 'A6',  // Mặc định cho GHTK
        orientation: 'portrait', // Mặc định cho GHTK
        ghnFormat: 'A5'  // Mặc định cho GHN
      };

      // Auto chọn format tối ưu dựa vào loại vận chuyển
      if (logisticInfo.shippingProvider === 'GHN') {
        // Sử dụng A5 cho GHN - chuẩn nhất và đẹp nhất khi in
        printOptions.ghnFormat = 'A5';
        this.logger.log(`Tự động chọn khổ ${printOptions.ghnFormat} cho đơn GHN`);
      } else if (logisticInfo.shippingProvider === 'GHTK') {
        // Sử dụng A6 cho GHTK - tiết kiệm giấy, vừa đủ thông tin
        printOptions.paperSize = 'A6';
        printOptions.orientation = 'portrait';
        this.logger.log(`Tự động chọn khổ ${printOptions.paperSize} hướng ${printOptions.orientation} cho đơn GHTK`);
      }

      // Gọi lại phương thức printOrder với các cài đặt đã tự động chọn
      return await this.printOrder(orderId, userId, printOptions);
    } catch (error) {
      this.logger.error(`Lỗi khi in đơn hàng tự động ${orderId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_PRINT_FAILED,
        `Lỗi khi in đơn hàng tự động: ${error.message}`
      );
    }
  }

  /**
   * Tính phí vận chuyển cho danh sách sản phẩm (public method cho API)
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param products Danh sách sản phẩm với productId và quantity
   * @param deliveryAddress Thông tin địa chỉ giao hàng (optional)
   * @param preferredCarrier Đơn vị vận chuyển ưu tiên
   * @param customerId ID customer để lấy địa chỉ mặc định (nếu không có deliveryAddress)
   * @param shippingPaymentType
   * @returns Thông tin phí vận chuyển
   */
  async calculateShippingFeeForProducts(
    userId: number,
    shopId: number,
    products: Array<{ productId: number; quantity: number }>,
    deliveryAddress?: any,
    preferredCarrier?: string,
    customerId?: number
    // ❌ Xóa receiverPaysShipping vì giờ lấy từ User Provider Shipment setting
  ): Promise<{
    carrier: string;
    fee: number;
    serviceType: string;
    estimatedDeliveryTime?: string;
    isEstimated?: boolean;
    configMessage?: string;
    note?: string;
  }> {
    try {
      this.logger.log(`Tính phí vận chuyển cho ${products.length} sản phẩm của userId=${userId}, shopId=${shopId}`);

      // Validate shopId thuộc về user
      const shopInfo = await this.userShopInfoService.getShopById(shopId, userId);
      if (!shopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Shop với ID ${shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // Xử lý địa chỉ giao hàng - sử dụng logic tương tự như tạo đơn hàng
      let customer: any = null;
      if (!deliveryAddress && customerId) {
        // Lấy thông tin customer nếu không có deliveryAddress
        const foundCustomer = await this.userConvertCustomerRepository.findById(customerId);
        if (!foundCustomer || foundCustomer.userId !== userId) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND,
            'Khách hàng không tồn tại hoặc không thuộc về bạn'
          );
        }
        customer = foundCustomer;
      } else if (!deliveryAddress && !customerId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          'Cần cung cấp địa chỉ giao hàng hoặc ID khách hàng'
        );
      }

      // Validate và lấy thông tin chi tiết sản phẩm từ database
      const productInfoData = await this.validateAndGetProductInfos(userId, products);

      // Xử lý thông tin logistics (sử dụng lại logic từ tạo đơn hàng)
      const logisticInfo = { deliveryAddress };
      const processedLogisticInfo = await this.processLogisticInfo(userId, logisticInfo, customer || { address: '', name: '', phone: '' });

      const finalLogisticInfo = {
        deliveryAddress: processedLogisticInfo.deliveryAddress
      };

      this.logger.log(`About to call calculateAndSelectShipping with preferredCarrier: ${preferredCarrier}`);

      // Sử dụng method private hiện có để tính phí với shopId
      const shippingResult = await this.calculateAndSelectShipping(
        userId,
        shopId,
        productInfoData,
        finalLogisticInfo,
        preferredCarrier
        // ❌ Xóa receiverPaysShipping vì giờ lấy từ User Provider Shipment setting
      );

      this.logger.log(`calculateAndSelectShipping returned:`, {
        carrier: shippingResult.carrier,
        fee: shippingResult.fee,
        serviceType: shippingResult.serviceType
      });

      this.logger.log(`Final shipping result before return:`, {
        carrier: shippingResult.carrier,
        fee: shippingResult.fee,
        serviceType: shippingResult.serviceType
      });

      return {
        carrier: shippingResult.carrier,
        fee: shippingResult.fee,
        serviceType: shippingResult.serviceType,
        estimatedDeliveryTime: '2-3 ngày', // Có thể cải thiện bằng cách tính toán thực tế
        note: 'Phí vận chuyển chính xác từ nhà cung cấp vận chuyển.'
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tính phí vận chuyển cho sản phẩm: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi tính phí vận chuyển: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật config GHN service với config của user
   */
  private async updateGHNServiceConfig(userId: number): Promise<void> {
    const userConfig = await this.userProviderShipmentService.getDecryptedConfig(userId, ProviderShipmentType.GHN);
    if (userConfig) {
      this.ghnShipmentService.setConfig({
        token: userConfig.token,
        shopId: userConfig.shopId,
        baseUrl: userConfig.baseUrl,
        timeout: userConfig.timeout || 30000,
        isTestMode: userConfig.isTestMode ?? true
      });
    }
  }

  /**
   * Cập nhật config GHTK service với config của user
   */
  private async updateGHTKServiceConfig(userId: number): Promise<void> {
    const userConfig = await this.userProviderShipmentService.getDecryptedConfig(userId, ProviderShipmentType.GHTK);
    if (userConfig) {
      this.ghtkShipmentService.setConfig({
        token: userConfig.token,
        baseUrl: userConfig.baseUrl,
        timeout: userConfig.timeout || 30000,
        isTestMode: userConfig.isTestMode ?? true
      });
    }
  }

  /**
   * Debug method để kiểm tra cấu hình provider của user
   * @param userId ID người dùng
   * @param carrier Đơn vị vận chuyển
   * @returns Thông tin debug chi tiết
   */
  async debugProviderConfig(userId: number, carrier: string): Promise<any> {
    try {
      this.logger.log(`Debug provider config for user ${userId}, carrier: ${carrier}`);

      let providerType: ProviderShipmentType;
      if (carrier === 'GHN') {
        providerType = ProviderShipmentType.GHN;
      } else if (carrier === 'GHTK') {
        providerType = ProviderShipmentType.GHTK;
      } else {
        return {
          error: `Đơn vị vận chuyển không được hỗ trợ: ${carrier}`
        };
      }

      // Lấy config từ service
      const config = await this.userProviderShipmentService.getDecryptedConfig(userId, providerType);

      // Kiểm tra validation
      const configCheck = await this.checkUserProviderConfig(userId, carrier);

      return {
        userId,
        carrier,
        providerType,
        hasConfig: !!config,
        config: config ? {
          hasToken: !!config.token,
          tokenLength: config.token ? config.token.length : 0,
          tokenPreview: config.token ? `${config.token.substring(0, 10)}...` : null,
          hasShopId: !!config.shopId,
          shopId: config.shopId ? `${config.shopId}` : null,
          baseUrl: config.baseUrl,
          timeout: config.timeout,
          isTestMode: config.isTestMode,
          type: config.type,
          name: config.name,
          configKeys: Object.keys(config)
        } : null,
        validation: {
          isConfigured: configCheck.isConfigured,
          message: configCheck.message
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Error in debugProviderConfig:`, error);
      return {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Validate payment logic nghiệp vụ
   * @param billInfo Thông tin hóa đơn
   * @param userId ID người dùng
   */
  private async validatePayment(billInfo: any, userId: number): Promise<void> {
    try {
      this.logger.log(`Validating payment for user ${userId}`, {
        paymentMethod: billInfo.paymentMethod,
        total: billInfo.total,
        codAmount: billInfo.codAmount
      });

      // 1. Không cho phép client tự set paymentStatus thành PAID
      if (billInfo.paymentStatus === PaymentStatusEnum.PAID) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_PAYMENT_STATUS,
          'Không thể tự set trạng thái thanh toán thành PAID. Trạng thái thanh toán sẽ được cập nhật tự động sau khi xử lý.'
        );
      }

      // 2. Validate COD amount limits
      if (billInfo.paymentMethod === PaymentMethodEnum.CASH) {
        const codAmount = billInfo.codAmount || billInfo.total || 0;

        // Giới hạn COD theo quy định thực tế của các đơn vị vận chuyển
        const COD_LIMIT = 50000000; // 50 triệu VND
        if (codAmount > COD_LIMIT) {
          throw new AppException(
            BUSINESS_ERROR_CODES.COD_LIMIT_EXCEEDED,
            `Số tiền COD không được vượt quá ${COD_LIMIT.toLocaleString('vi-VN')} VND. Vui lòng chọn phương thức thanh toán khác cho đơn hàng có giá trị cao.`
          );
        }

        // Validate COD amount phải khớp với total nếu là thanh toán toàn bộ bằng COD
        if (codAmount > 0 && Math.abs(codAmount - (billInfo.total || 0)) > 1000) {
          this.logger.warn(`COD amount mismatch: COD=${codAmount}, Total=${billInfo.total}`);
        }
      }

      // 3. Validate total amount
      const totalAmount = billInfo.total || 0;
      if (totalAmount <= 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PAYMENT_VALIDATION_FAILED,
          'Tổng tiền đơn hàng phải lớn hơn 0'
        );
      }

      // 4. Validate maximum order value
      const MAX_ORDER_VALUE = 500000000; // 500 triệu VND
      if (totalAmount > MAX_ORDER_VALUE) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PAYMENT_VALIDATION_FAILED,
          `Giá trị đơn hàng không được vượt quá ${MAX_ORDER_VALUE.toLocaleString('vi-VN')} VND`
        );
      }

      // 5. Validate credit limit cho các phương thức thanh toán khác COD
      if (billInfo.paymentMethod !== PaymentMethodEnum.CASH) {
        await this.validateCreditLimit(userId, totalAmount);
      }

      // 6. Validate discount không được lớn hơn subtotal
      const discount = billInfo.discount || 0;
      const subtotal = billInfo.subtotal || 0;
      if (discount > subtotal) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PAYMENT_VALIDATION_FAILED,
          'Số tiền giảm giá không được lớn hơn tổng tiền hàng'
        );
      }

      this.logger.log(`Payment validation passed for user ${userId}`);
    } catch (error) {
      this.logger.error(`Payment validation failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate credit limit cho user
   * @param userId ID người dùng
   * @param amount Số tiền cần validate
   */
  private async validateCreditLimit(userId: number, amount: number): Promise<void> {
    try {
      // TODO: Implement credit limit check từ user profile hoặc payment service
      // Hiện tại set limit cứng, sau này có thể lấy từ database
      const DEFAULT_CREDIT_LIMIT = 100000000; // 100 triệu VND

      if (amount > DEFAULT_CREDIT_LIMIT) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CREDIT_LIMIT_EXCEEDED,
          `Giá trị đơn hàng vượt quá hạn mức tín dụng ${DEFAULT_CREDIT_LIMIT.toLocaleString('vi-VN')} VND. Vui lòng liên hệ để tăng hạn mức hoặc chọn phương thức thanh toán khác.`
        );
      }

      // TODO: Có thể thêm logic kiểm tra:
      // - Số dư tài khoản nếu là chuyển khoản
      // - Lịch sử thanh toán của user
      // - Blacklist check

      this.logger.log(`Credit limit validation passed for user ${userId}, amount: ${amount}`);
    } catch (error) {
      this.logger.error(`Credit limit validation failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa nhiều đơn hàng
   * @param userId ID người dùng
   * @param bulkDeleteDto DTO chứa danh sách ID đơn hàng cần xóa
   * @returns Kết quả xóa nhiều đơn hàng
   */
  @Transactional()
  async bulkDeleteOrders(userId: number, bulkDeleteDto: BulkDeleteUserOrderDto): Promise<BulkDeleteUserOrderResponseDto> {
    try {
      this.logger.log(`Xử lý xóa nhiều đơn hàng cho user ${userId} với ${bulkDeleteDto.orderIds.length} đơn hàng`);

      // Kiểm tra danh sách đơn hàng không rỗng
      if (!bulkDeleteDto.orderIds || bulkDeleteDto.orderIds.length === 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_VALIDATION_FAILED,
          'Danh sách đơn hàng không được để trống'
        );
      }

      // Thực hiện xóa nhiều đơn hàng thông qua repository
      const deleteResult = await this.userOrderRepository.bulkDeleteOrders(bulkDeleteDto.orderIds, userId);

      // Tạo kết quả chi tiết cho từng đơn hàng
      const results: BulkDeleteUserOrderResultItemDto[] = [];

      // Thêm kết quả cho các đơn hàng xóa thành công
      deleteResult.deletedOrderIds.forEach(orderId => {
        results.push({
          orderId,
          status: 'success',
          message: 'Xóa đơn hàng thành công'
        });
      });

      // Thêm kết quả cho các đơn hàng xóa thất bại
      deleteResult.failedOrderIds.forEach(orderId => {
        results.push({
          orderId,
          status: 'error',
          message: 'Đơn hàng không tồn tại hoặc không thuộc về bạn'
        });
      });

      const response: BulkDeleteUserOrderResponseDto = {
        totalRequested: bulkDeleteDto.orderIds.length,
        successCount: deleteResult.deletedCount,
        failureCount: deleteResult.failedCount,
        results,
        message: `Xóa thành công ${deleteResult.deletedCount}/${bulkDeleteDto.orderIds.length} đơn hàng`,
      };

      this.logger.log(`Kết quả xóa nhiều đơn hàng cho user ${userId}: ${response.message}`);

      // Nếu có đơn hàng không thể xóa, ném exception với mã lỗi phù hợp
      if (deleteResult.failedCount > 0 && deleteResult.deletedCount === 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_FAILED,
          response.message
        );
      } else if (deleteResult.failedCount > 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_PARTIAL_SUCCESS,
          response.message
        );
      }

      return response;
    } catch (error) {
      this.logger.error(`Lỗi xử lý xóa nhiều đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_BULK_DELETE_FAILED,
        `Lỗi xử lý xóa nhiều đơn hàng: ${error.message}`
      );
    }
  }

}
