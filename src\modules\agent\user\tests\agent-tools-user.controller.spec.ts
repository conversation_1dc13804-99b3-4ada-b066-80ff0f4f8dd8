import { Test, TestingModule } from '@nestjs/testing';
import { AgentToolsUserController } from '../controllers/agent-tools-user.controller';
import { AgentToolsUserService } from '../services/agent-tools-user.service';
import { AgentToolsQueryDto, AddAgentToolsDto, RemoveAgentToolsDto } from '../dto/agent-tools';

describe('AgentToolsUserController', () => {
  let controller: AgentToolsUserController;
  let service: AgentToolsUserService;

  const mockService = {
    getAgentTools: jest.fn(),
    addToolsToAgent: jest.fn(),
    removeToolsFromAgent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentToolsUserController],
      providers: [
        {
          provide: AgentToolsUserService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<AgentToolsUserController>(AgentToolsUserController);
    service = module.get<AgentToolsUserService>(AgentToolsUserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAgentTools', () => {
    it('should return paginated tools list', async () => {
      // Arrange
      const userId = 1;
      const agentId = 'agent-uuid';
      const queryDto: AgentToolsQueryDto = { page: 1, limit: 10 };
      const mockResult = {
        items: [
          {
            id: 'tool-uuid',
            toolName: 'test_tool',
            toolDescription: 'Test tool description',
            createdAt: 1703123456789,
            updatedAt: 1703123456789,
          },
        ],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
          hasItems: true,
        },
      };

      mockService.getAgentTools.mockResolvedValue(mockResult);

      // Act
      const result = await controller.getAgentTools(userId, agentId, queryDto);

      // Assert
      expect(service.getAgentTools).toHaveBeenCalledWith(agentId, userId, queryDto);
      expect(result.result).toEqual(mockResult);
      expect(result.message).toBe('Lấy danh sách tools thành công');
    });
  });

  describe('addToolsToAgent', () => {
    it('should add tools to agent successfully', async () => {
      // Arrange
      const userId = 1;
      const agentId = 'agent-uuid';
      const addDto: AddAgentToolsDto = {
        toolIds: ['tool-uuid-1', 'tool-uuid-2'],
      };
      const mockResult = {
        processedCount: 2,
        skippedCount: 0,
        skippedIds: [],
        totalRequested: 2,
      };

      mockService.addToolsToAgent.mockResolvedValue(mockResult);

      // Act
      const result = await controller.addToolsToAgent(userId, agentId, addDto);

      // Assert
      expect(service.addToolsToAgent).toHaveBeenCalledWith(agentId, userId, addDto);
      expect(result.result).toEqual(mockResult);
      expect(result.message).toBe('Thêm tools vào agent thành công');
    });
  });

  describe('removeToolsFromAgent', () => {
    it('should remove tools from agent successfully', async () => {
      // Arrange
      const userId = 1;
      const agentId = 'agent-uuid';
      const removeDto: RemoveAgentToolsDto = {
        toolIds: ['tool-uuid-1', 'tool-uuid-2'],
      };
      const mockResult = {
        processedCount: 2,
        skippedCount: 0,
        skippedIds: [],
        totalRequested: 2,
      };

      mockService.removeToolsFromAgent.mockResolvedValue(mockResult);

      // Act
      const result = await controller.removeToolsFromAgent(userId, agentId, removeDto);

      // Assert
      expect(service.removeToolsFromAgent).toHaveBeenCalledWith(agentId, userId, removeDto);
      expect(result.result).toEqual(mockResult);
      expect(result.message).toBe('Gỡ bỏ tools khỏi agent thành công');
    });
  });
});
