# Batch Create Products API

## Tổng quan

API `POST /user/products/batch` cho phép tạo nhiều sản phẩm cùng lúc với tối đa 50 sản phẩm mỗi lần. API sử dụng service tạo 1 sản phẩm để đảm bảo tính nhất quán và xử lý từng sản phẩm một cách tuần tự.

## Cải tiến so với phiên bản cũ

### ✅ Những gì đã được cải thiện:

1. **Type Safety**: Sử dụng `CreatedProductDto` union type thay vì `BusinessCreateProductDto`
2. **Validation mạnh mẽ**: Thêm validation cho input và giới hạn 50 sản phẩm
3. **Error Handling tốt hơn**: Chi tiết hơn về lỗi từng sản phẩm
4. **Transaction Safety**: Sử dụng `@Transactional()` decorator
5. **Logging cải thiện**: Log chi tiết hơn cho debugging
6. **Response Structure**: Type-safe response với `ProductResponseDto[]`

### ❌ Những gì đã loại bỏ:

1. **convertToTypeSafeDto method**: Không cần thiết vì đã sử dụng đúng type từ đầu
2. **BusinessBatchCreateProductDto**: Thay thế bằng `BatchCreateProductsDto`
3. **any types**: Thay thế bằng type-safe interfaces

## Endpoint

```
POST /user/products/batch
```

## Request Body

```typescript
{
  products: CreatedProductDto[] // Tối đa 50 sản phẩm
}
```

### Supported Product Types

1. **PhysicalProductCreateDto** - Sản phẩm vật lý
2. **DigitalProductCreateDto** - Sản phẩm số
3. **EventProductCreateDto** - Sự kiện
4. **ServiceProductCreateDto** - Dịch vụ
5. **ComboProductCreateDto** - Sản phẩm combo

## Response Structure

```typescript
{
  successProducts: ProductResponseDto[];
  failedProducts: Array<{
    index: number;
    productName: string;
    error: string;
  }>;
  totalProducts: number;
  successCount: number;
  failedCount: number;
}
```

## Example Request

```json
{
  "products": [
    {
      "name": "Áo thun nam",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 300000,
        "salePrice": 250000,
        "currency": "VND"
      },
      "description": "Áo thun nam chất liệu cotton cao cấp",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["thời trang", "nam"],
      "shipmentConfig": {
        "widthCm": 20,
        "heightCm": 5,
        "lengthCm": 25,
        "weightGram": 150
      },
      "inventory": {
        "availableQuantity": 100,
        "sku": "SHIRT-001",
        "barcode": "1234567890123"
      }
    },
    {
      "name": "Khóa học lập trình",
      "productType": "DIGITAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 500000,
        "salePrice": 400000,
        "currency": "VND"
      },
      "description": "Khóa học lập trình từ cơ bản đến nâng cao",
      "imagesMediaTypes": ["image/jpeg"],
      "tags": ["giáo dục", "lập trình"],
      "deliveryMethod": "EMAIL",
      "deliveryTiming": "IMMEDIATE",
      "outputType": "ACCESS_CODE"
    }
  ]
}
```

## Example Response

```json
{
  "data": {
    "successProducts": [
      {
        "id": 1,
        "name": "Áo thun nam",
        "productType": "PHYSICAL",
        "price": {
          "listPrice": 300000,
          "salePrice": 250000,
          "currency": "VND"
        },
        "uploadUrls": {
          "imagesUploadUrls": ["https://s3.amazonaws.com/..."]
        }
      }
    ],
    "failedProducts": [
      {
        "index": 1,
        "productName": "Khóa học lập trình",
        "error": "Tên sản phẩm đã tồn tại"
      }
    ],
    "totalProducts": 2,
    "successCount": 1,
    "failedCount": 1
  },
  "message": "Tạo thành công 1/2 sản phẩm",
  "success": true
}
```

## Error Handling

### Validation Errors

- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **400 Bad Request**: Vượt quá giới hạn 50 sản phẩm
- **400 Bad Request**: Danh sách sản phẩm rỗng

### Individual Product Errors

Lỗi từng sản phẩm sẽ được ghi trong `failedProducts` array với:
- `index`: Vị trí trong mảng gốc
- `productName`: Tên sản phẩm bị lỗi
- `error`: Mô tả lỗi chi tiết

## Implementation Details

### Service Method

```typescript
@Transactional()
async batchCreateProducts(
  batchCreateDto: { products: CreatedProductDto[] },
  userId: number,
): Promise<{
  successProducts: ProductResponseDto[];
  failedProducts: Array<{
    index: number;
    productName: string;
    error: string;
  }>;
  totalProducts: number;
  successCount: number;
  failedCount: number;
}>
```

### Key Features

1. **Sequential Processing**: Xử lý từng sản phẩm tuần tự để tránh conflict
2. **Individual Error Handling**: Lỗi 1 sản phẩm không ảnh hưởng đến các sản phẩm khác
3. **Comprehensive Logging**: Log chi tiết cho debugging
4. **Type Safety**: Sử dụng TypeScript types đầy đủ
5. **Transaction Support**: Đảm bảo data consistency

## Best Practices

1. **Batch Size**: Không vượt quá 50 sản phẩm mỗi lần
2. **Error Handling**: Luôn kiểm tra `failedProducts` trong response
3. **Retry Logic**: Implement retry cho các sản phẩm failed nếu cần
4. **Monitoring**: Monitor `successCount` và `failedCount` để đánh giá hiệu suất

## Migration Guide

### Từ BusinessBatchCreateProductDto sang BatchCreateProductsDto

```typescript
// Cũ
const oldDto: BusinessBatchCreateProductDto = {
  products: [/* BusinessCreateProductDto[] */]
};

// Mới
const newDto: BatchCreateProductsDto = {
  products: [/* CreatedProductDto[] */]
};
```

### Response Type Changes

```typescript
// Cũ - any type
const result: any = await batchCreateProducts(dto, userId);

// Mới - Type-safe
const result: {
  successProducts: ProductResponseDto[];
  failedProducts: Array<{
    index: number;
    productName: string;
    error: string;
  }>;
  totalProducts: number;
  successCount: number;
  failedCount: number;
} = await batchCreateProducts(dto, userId);
```
