import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  UserProductRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { BusinessUpdateProductDto } from '../../../dto';
import { PriceTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UpdateProductResult } from './update-product-orchestrator';

/**
 * Processor chuyên xử lý cập nhật sản phẩm sự kiện (EVENT)
 * Xử lý event date, location, ticket types, max attendees
 */
@Injectable()
export class EventProductUpdateProcessor {
  private readonly logger = new Logger(EventProductUpdateProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
  ) {}

  /**
   * Cập nhật sản phẩm sự kiện hoàn chỉnh
   */
  @Transactional()
  async updateEventProduct(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    _userId: number,
  ): Promise<UpdateProductResult> {
    this.logger.log(
      `Updating EVENT product: ${product.name} (ID: ${product.id}) for user: ${_userId}`,
    );

    // BƯỚC 1: Validate dữ liệu đầu vào cho sự kiện
    this.validateEventProductData(updateDto);

    // BƯỚC 2: Cập nhật event-specific fields
    this.updateEventFields(product, updateDto);

    // BƯỚC 3: Xử lý giá đặc biệt cho EVENT (price = null)
    this.updateEventPricing(product, updateDto);

    // BƯỚC 4: Đảm bảo shipment config = 0 cho sự kiện
    this.ensureZeroShipmentConfig(product);

    // BƯỚC 5: Lưu sản phẩm đã cập nhật
    const updatedProduct = await this.userProductRepository.save(product);

    // BƯỚC 6: Xử lý ticket types trong advanced info
    await this.processTicketTypesUpdate(updatedProduct, updateDto);

    return {
      product: updatedProduct,
      imagesUploadUrls: [],
      advancedImagesUploadUrls: [],
      classificationUploadUrls: [],
      classifications: [],
      inventory: null, // Event products don't have inventory
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sự kiện
   */
  private validateEventProductData(updateDto: BusinessUpdateProductDto): void {
    const eventAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!eventAdvancedInfo) {
      return;
    }

    // Validate event date nếu có
    if (eventAdvancedInfo.eventDate) {
      const eventDate = new Date(
        eventAdvancedInfo.eventDate as string | number | Date,
      );
      const now = new Date();

      if (eventDate <= now) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Ngày sự kiện phải trong tương lai',
        );
      }
    }

    // Validate max attendees nếu có
    if (
      eventAdvancedInfo.maxAttendees !== undefined &&
      (eventAdvancedInfo.maxAttendees as number) <= 0
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Số lượng người tham dự tối đa phải lớn hơn 0',
      );
    }

    // Validate purchase count nếu có
    if (
      eventAdvancedInfo.purchaseCount !== undefined &&
      (eventAdvancedInfo.purchaseCount as number) < 0
    ) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Purchase count không thể âm',
      );
    }

    // Validate ticket types nếu có trong advanced info
    if (eventAdvancedInfo.ticketTypes) {
      this.validateTicketTypes(eventAdvancedInfo.ticketTypes as unknown[]);
    }
  }

  /**
   * Validate ticket types
   */
  private validateTicketTypes(ticketTypes: unknown[]): void {
    if (!Array.isArray(ticketTypes) || ticketTypes.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Sự kiện phải có ít nhất một loại vé',
      );
    }

    for (const ticketType of ticketTypes) {
      const ticketTypeObj = ticketType as Record<string, unknown>;

      // Validate required fields
      if (!ticketTypeObj.name || !ticketTypeObj.price) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Loại vé phải có tên và giá',
        );
      }

      const price = ticketTypeObj.price as Record<string, unknown>;
      // Validate price
      if (!price.listPrice || (price.listPrice as number) < 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Giá vé không hợp lệ',
        );
      }

      // Validate max quantity
      if (
        ticketTypeObj.maxQuantity !== undefined &&
        (ticketTypeObj.maxQuantity as number) <= 0
      ) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Số lượng vé tối đa phải lớn hơn 0',
        );
      }
    }
  }

  /**
   * Cập nhật các trường đặc thù cho sự kiện
   */
  private updateEventFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const eventAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!eventAdvancedInfo) {
      return;
    }

    // Cập nhật event date
    if (eventAdvancedInfo.eventDate !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.eventDate = eventAdvancedInfo.eventDate as string;
      this.logger.log(
        `Updated event date to: ${eventAdvancedInfo.eventDate as string}`,
      );
    }

    // Cập nhật event location
    if (eventAdvancedInfo.eventLocation !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.eventLocation =
        eventAdvancedInfo.eventLocation as string;
      this.logger.log(
        `Updated event location to: ${eventAdvancedInfo.eventLocation as string}`,
      );
    }

    // Cập nhật max attendees
    if (eventAdvancedInfo.maxAttendees !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.maxAttendees = eventAdvancedInfo.maxAttendees as number;
      this.logger.log(
        `Updated max attendees to: ${eventAdvancedInfo.maxAttendees as number}`,
      );
    }

    // Cập nhật purchase count
    if (eventAdvancedInfo.purchaseCount !== undefined) {
      product.metadata = product.metadata || {};
      product.metadata.purchaseCount =
        eventAdvancedInfo.purchaseCount as number;
      this.logger.log(
        `Updated purchase count to: ${eventAdvancedInfo.purchaseCount as number}`,
      );
    }
  }

  /**
   * Xử lý giá đặc biệt cho EVENT products
   */
  private updateEventPricing(
    product: UserProduct,
    _updateDto: BusinessUpdateProductDto,
  ): void {
    // EVENT products có giá = null, giá thực tế nằm trong ticket types
    // _updateDto is used for future extensibility
    void _updateDto;
    product.price = null;
    product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE;

    this.logger.log(
      'Set price to null for EVENT product (price is in ticket types)',
    );
  }

  /**
   * Đảm bảo shipment config = 0 cho sự kiện
   */
  private ensureZeroShipmentConfig(product: UserProduct): void {
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0,
    };
    this.logger.log('Set shipment config to zero for EVENT product');
  }

  /**
   * Xử lý cập nhật ticket types trong advanced info
   */
  private async processTicketTypesUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    const eventAdvancedInfo = updateDto.advancedInfo as unknown as Record<
      string,
      unknown
    >;

    if (!eventAdvancedInfo?.ticketTypes || !product.detail_id) {
      return;
    }

    try {
      this.logger.log(`Updating ticket types for EVENT product ${product.id}`);

      // Tìm advanced info hiện tại
      const existingAdvancedInfo =
        await this.productAdvancedInfoRepository.findOne({
          where: { id: product.detail_id },
        });

      if (existingAdvancedInfo) {
        // Xử lý ticket types
        const processedTicketTypes = (
          eventAdvancedInfo.ticketTypes as unknown[]
        ).map((ticketType: unknown) => {
          const ticketTypeObj = ticketType as Record<string, unknown>;
          // Loại bỏ imagesMediaTypes nếu có
          const { imagesMediaTypes, ...ticketTypeWithoutImages } =
            ticketTypeObj;
          // Sử dụng imagesMediaTypes để tránh lỗi unused variable
          void imagesMediaTypes;
          return ticketTypeWithoutImages;
        });

        // Cập nhật ticket types trong advanced info
        existingAdvancedInfo.ticketTypes = processedTicketTypes as never;
        existingAdvancedInfo.updatedAt = Date.now();

        await this.productAdvancedInfoRepository.save(existingAdvancedInfo);
        this.logger.log(
          `Successfully updated ticket types for EVENT product ${product.id}`,
        );
      }
    } catch (error) {
      const errorMessage = (error as Error).message;
      const errorStack = (error as Error).stack;
      this.logger.error(
        `Error updating ticket types for EVENT product ${product.id}: ${errorMessage}`,
        errorStack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật ticket types: ${errorMessage}`,
      );
    }
  }
}
