import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, IsUUID, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@dto/query.dto';

/**
 * Enum cho các trường sắp xếp khách hàng chuyển đổi (Admin)
 */
export enum AdminUserConvertCustomerSortField {
  ID = 'id',
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  PLATFORM = 'platform',
}

/**
 * DTO cho các tham số truy vấn danh sách khách hàng chuyển đổi
 */
export class QueryUserConvertCustomerDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID người dùng sở hữu khách hàng',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @IsOptional()
  @IsUUID('4')
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Nền tảng nguồn (Facebook, Web,...)',
    example: 'Facebook'
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo từ (timestamp)',
    example: 1625097600000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtFrom?: number;

  @ApiPropertyOptional({
    description: 'Thời gian tạo đến (timestamp)',
    example: 1625184000000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtTo?: number;

  /**
   * Ghi đè thuộc tính sortBy từ QueryDto để sử dụng enum cụ thể
   * @example "createdAt"
   */
  @ApiPropertyOptional({
    description: 'Trường sắp xếp',
    enum: AdminUserConvertCustomerSortField,
    default: AdminUserConvertCustomerSortField.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AdminUserConvertCustomerSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(AdminUserConvertCustomerSortField).join(', ')}`,
  })
  override sortBy?: AdminUserConvertCustomerSortField = AdminUserConvertCustomerSortField.CREATED_AT;

  /**
   * Ghi đè thuộc tính sortDirection từ QueryDto để thay đổi giá trị mặc định
   * @example "DESC"
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection, {
    message: `Hướng sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,
  })
  override sortDirection?: SortDirection = SortDirection.DESC;
}
