# Cập nhật Inventory từ Object thành Array

## Tóm tắt thay đổi
Đã cập nhật hệ thống để hỗ trợ quản lý tồn kho sản phẩm ở nhiều kho khác nhau bằng cách thay đổi từ một `ProductInventoryDto` thành mảng `ProductInventoryDto[]`.

## Các file đã được cập nhật

### 1. DTO Request - Physical Product Create
**File:** `src/modules/business/user/dto/request/create/physical-product.dto.ts`
- Thay đổi `inventory: ProductInventoryDto` thành `inventory: ProductInventoryDto[]`
- Cập nhật validation từ `@IsObject()` thành `@IsArray()` và `@ValidateNested({ each: true })`
- Cập nhật ApiProperty description và type

### 2. DTO Product Inventory
**File:** `src/modules/business/user/dto/product-inventory.dto.ts`
- Thêm field `inventoryId?: number` để hỗ trợ sử dụng inventory có sẵn
- Giữ nguyên các field khác: `warehouseId`, `availableQuantity`, `sku`, `barcode`

### 3. DTO Update Product
**File:** `src/modules/business/user/dto/update-product.dto.ts`
- Thay đổi `inventory?: ProductInventoryDto` thành `inventory?: ProductInventoryDto[]`
- Cập nhật validation từ `@IsObject()` thành `@IsArray()` và `@ValidateNested({ each: true })`
- Cập nhật examples để hiển thị mảng inventory

### 4. DTO Response - Physical Product
**File:** `src/modules/business/user/dto/response/physical-product-response.dto.ts`
- Thay đổi `inventory?: PhysicalInventoryResponseDto` thành `inventory?: PhysicalInventoryResponseDto[]`
- Cập nhật ApiProperty để hiển thị array type

### 5. Physical Product Processor
**File:** `src/modules/business/user/services/processors/create/physical-product.processor.ts`
- Thêm import `PriceTypeEnum`
- Cập nhật method `processPhysicalInventory` để xử lý mảng `inventoryDtos: any[]`
- Cập nhật validation để kiểm tra mảng inventory
- Cập nhật return type từ `Promise<any>` thành `Promise<any[]>`
- Sử dụng `inventoryDtos.map()` để xử lý từng inventory trong mảng

### 6. Physical Product Handler
**File:** `src/modules/business/user/services/product-creation/handlers/physical-product.handler.ts`
- Cập nhật method `processInventory` để xử lý mảng inventory
- Thay đổi return type từ `Promise<any>` thành `Promise<any[]>`
- Sử dụng vòng lặp `for` để xử lý từng inventory trong mảng

## Validation Logic
- Kiểm tra inventory phải là mảng và có ít nhất 1 phần tử
- Mỗi inventory trong mảng phải có `warehouseId` hoặc `inventoryId`
- Giữ nguyên validation cho price (bắt buộc cho sản phẩm vật lý)

## Tương thích ngược
- API vẫn hỗ trợ cả hai trường hợp:
  - `inventoryId`: Sử dụng inventory có sẵn
  - `warehouseId` + thông tin khác: Tạo inventory mới
- Response trả về mảng inventory thay vì object đơn lẻ

## Ví dụ sử dụng

### Request tạo sản phẩm với nhiều kho:
```json
{
  "name": "Áo thun nam",
  "productType": "PHYSICAL",
  "inventory": [
    {
      "warehouseId": 1,
      "availableQuantity": 100,
      "sku": "SKU-001",
      "barcode": "1234567890123"
    },
    {
      "warehouseId": 2,
      "availableQuantity": 50,
      "sku": "SKU-002",
      "barcode": "1234567890124"
    }
  ],
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  }
}
```

### Response:
```json
{
  "inventory": [
    {
      "id": 1,
      "warehouseId": 1,
      "availableQuantity": 100,
      "sku": "SKU-001",
      "barcode": "1234567890123"
    },
    {
      "id": 2,
      "warehouseId": 2,
      "availableQuantity": 50,
      "sku": "SKU-002",
      "barcode": "1234567890124"
    }
  ]
}
```
