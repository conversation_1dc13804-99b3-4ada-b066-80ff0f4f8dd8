import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsArray,
  IsObject,
  ValidateNested,
  IsEnum,
  Min,
} from 'class-validator';
import { HasPriceDto } from '../price.dto';
import { CustomFieldInputDto } from '@modules/business/user/dto';

export type DeliveryMethodType = 'DASHBOARD' | 'EMAIL' | 'SMS' | 'DIRECT_MESSAGE' | 'ZALO' | 'AUTO_ACTIVE';
export type DeliveryTimingType = 'IMMEDIATE' | 'DELAYED';
 
/**
 * DTO cho Digital Fulfillment Flow
 */
export class DigitalFulfillmentFlowDto {
  @ApiProperty({
    description: 'Phương thức giao hàng',
    example: 'EMAIL',
    enum: ['DASHBOARD', 'EMAIL', 'SMS', 'DIRECT_MESSAGE', 'ZALO', 'AUTO_ACTIVE'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['DASHBOARD', 'EMAIL', 'SMS', 'DIRECT_MESSAGE', 'ZALO', 'AUTO_ACTIVE'])
  deliveryMethod: DeliveryMethodType;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'IMMEDIATE',
    enum: ['IMMEDIATE', 'DELAYED'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['IMMEDIATE', 'DELAYED'])
  deliveryTiming: DeliveryTimingType;
}

export type OutputType = 'DOWNLOAD_LINK' | 'ACCESS_CODE' | 'ACCOUNT_INFO' | 'CONTENT';

/**
 * DTO cho Digital Output
 */
export class DigitalOutputDto {
  @ApiProperty({
    description: 'Loại output',
    example: 'online_course',
  })
  @IsString()
  @IsNotEmpty()
  outputType: string;

  @ApiProperty({
    description: 'Link truy cập',
    example: 'https://course.example.com/activate?token=abc123',
  })
  @IsString()
  @IsNotEmpty()
  accessLink: string;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học',
  })
  @IsString()
  @IsNotEmpty()
  usageInstructions: string;
}

/**
 * DTO cho Variant
 */
export class VariantDto {
  @ApiProperty({
    description: 'Tên variant',
    example: 'Basic',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'SKU',
    example: 'BASIC-001',
  })
  @IsString()
  @IsNotEmpty()
  sku: string;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 1,
  })
  @IsNumber()
  @Min(0)
  availableQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giá variant',
    type: HasPriceDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => HasPriceDto)
  price: HasPriceDto;

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];

  @ApiProperty({
    description: 'Danh sách loại hình ảnh',
    type: [String],
    example: ['image/jpeg'],
  })
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes: string[];

  @ApiProperty({
    description: 'Mô tả variant',
    example: 'Phiên bản cơ bản - Học React cơ bản',
  })
  @IsString()
  @IsNotEmpty()
  description: string;
}

/**
 * DTO cho Variant Metadata
 */
export class VariantMetadataDto {
  @ApiProperty({
    description: 'Danh sách variants',
    type: [VariantDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => VariantDto)
  variants: VariantDto[];
}

/**
 * DTO cho Digital Advanced Info
 */
export class DigitalAdvancedInfoDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  purchaseCount: number;

  @ApiProperty({
    description: 'Digital fulfillment flow',
    type: DigitalFulfillmentFlowDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalFulfillmentFlowDto)
  digitalFulfillmentFlow: DigitalFulfillmentFlowDto;

  @ApiProperty({
    description: 'Digital output',
    type: DigitalOutputDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalOutputDto)
  digitalOutput: DigitalOutputDto;

  @ApiProperty({
    description: 'Variant metadata',
    type: VariantMetadataDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => VariantMetadataDto)
  variantMetadata: VariantMetadataDto;
}
